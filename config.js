// Configuration for WebRTC Frontend
// This file determines the backend URL based on the environment

// Function to determine the backend URL
function getBackendUrl() {
    // Check for environment-specific configuration
    const hostname = window.location.hostname;

    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        // Local development
        return 'http://localhost:5055';
    } else if (hostname.includes('run.app')) {
        // Google Cloud Run - use environment variable or construct URL
        // You can set this via environment variable in Cloud Run
        const backendUrl = (window.BACKEND_URL) || 'https://webrtc-backend-985795046666.us-central1.run.app';
        return backendUrl;
    } else if (hostname.includes('herokuapp.com')) {
        // Heroku
        return `https://your-backend-app.herokuapp.com`;
    } else if (hostname.includes('azurewebsites.net')) {
        // Azure
        return `https://your-backend-app.azurewebsites.net`;
    } else {
        // Production - construct URL based on current domain
        // Assumes backend is deployed with a predictable URL pattern
        return `${window.location.protocol}//api.${hostname}`;
    }
}

// Export the configuration
window.WebRTCConfig = {
    backendUrl: getBackendUrl(),
    // Add other configuration options here
    debug: window.location.hostname === 'localhost',
    retryAttempts: 3,
    retryDelay: 1000,
    // Performance optimization: prefer direct API calls for speed
    preferDirectApiCalls: true,
    // Timeout for API calls (in milliseconds)
    apiTimeout: 10000
};

// Log configuration for debugging
if (window.WebRTCConfig.debug) {
    console.log('WebRTC Configuration:', window.WebRTCConfig);
}
