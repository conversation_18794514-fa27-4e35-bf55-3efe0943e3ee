# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
dbname=call_centre_analytics
user=chatbot_user
password=your_database_password
host=your_database_host
port=5432

# Security
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Model Configuration
MODEL=gpt-4o-mini-realtime-preview-2024-12-17
# Alternative model: gpt-4o-realtime-preview-2024-12-17

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token

# WebRTC Configuration
OPENAI_SESSION_URL=https://api.openai.com/v1/realtime/sessions
OPENAI_API_URL=https://api.openai.com/v1/realtime
VOICE=ash
DEFAULT_INSTRUCTIONS=You are helpful and have some tools installed.\n\nYou speak to the user in the urdu language. Use simple vocubularly and speak clearly.
PORT=5055
