#!/usr/bin/env python3
"""
Test script for the integrated WebRTC backend
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:5055"
TEST_ORG_ID = "nayatel"

def test_health_endpoint():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check status: {response.status_code}")
        print(f"Health check response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_home_endpoint():
    """Test the home endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Home endpoint status: {response.status_code}")
        print(f"Home endpoint response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Home endpoint failed: {e}")
        return False

def test_session_config_endpoint():
    """Test the session configuration endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/session-config/{TEST_ORG_ID}")
        print(f"Session config status: {response.status_code}")
        if response.status_code == 200:
            config = response.json()
            print(f"Session config response: {json.dumps(config, indent=2)}")
        elif response.status_code == 404:
            print(f"No configuration found for organisation: {TEST_ORG_ID}")
        else:
            print(f"Session config error: {response.text}")
        return True  # 404 is expected if no data exists
    except Exception as e:
        print(f"Session config test failed: {e}")
        return False

def test_rtc_connect_endpoint():
    """Test the RTC connect endpoint (basic test without real SDP)"""
    try:
        # This will fail because we don't have a real SDP, but it tests the endpoint exists
        response = requests.post(f"{BASE_URL}/api/rtc-connect", data="test-sdp")
        print(f"RTC connect status: {response.status_code}")
        print(f"RTC connect response: {response.text[:200]}...")  # First 200 chars
        return True  # Any response means the endpoint is working
    except Exception as e:
        print(f"RTC connect test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing integrated WebRTC backend...")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Home Endpoint", test_home_endpoint),
        ("Session Config", test_session_config_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- Testing {test_name} ---")
        result = test_func()
        results.append((test_name, result))
        print(f"{test_name}: {'PASS' if result else 'FAIL'}")
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
