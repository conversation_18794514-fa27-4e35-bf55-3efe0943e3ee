#!/bin/bash

# Deployment script for WebRTC Backend

set -e

# Configuration
IMAGE_NAME="webrtc-backend"
TAG=${1:-latest}
PLATFORM=${2:-"gcp"}

echo "Deploying WebRTC Backend..."
echo "Platform: ${PLATFORM}"
echo "Image: ${IMAGE_NAME}:${TAG}"

case $PLATFORM in
    "docker")
        echo "Deploying with Docker Compose..."
        docker-compose down
        docker-compose up -d
        echo "Application deployed at: http://localhost:5055"
        ;;
    
    "gcp")
        echo "Deploying to Google Cloud Run..."
        PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
        
        if [ -z "$PROJECT_ID" ]; then
            echo "Error: GCP_PROJECT_ID not set and no default project configured"
            echo "Please set GCP_PROJECT_ID environment variable or run: gcloud config set project YOUR_PROJECT_ID"
            exit 1
        fi
        
        echo "Using project: $PROJECT_ID"
        
        # Build and push to Container Registry
        echo "Building image..."
        docker build -t gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG} .
        
        echo "Pushing image to Container Registry..."
        docker push gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG}
        
        # Deploy to Cloud Run
        echo "Deploying to Cloud Run..."
        gcloud run deploy ${IMAGE_NAME} \
            --image gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG} \
            --platform managed \
            --region us-central1 \
            --allow-unauthenticated \
            --port 5055 \
            --memory 2Gi \
            --cpu 1 \
            --timeout 300 \
            --concurrency 80 \
            --max-instances 100 
        
        echo "Getting service URL..."
        SERVICE_URL=$(gcloud run services describe ${IMAGE_NAME} --platform managed --region us-central1 --format 'value(status.url)')
        echo "Backend deployed at: $SERVICE_URL"
        echo ""
        echo "IMPORTANT: Update your frontend config.js with this backend URL:"
        echo "Replace 'https://webrtc-backend-YOUR_PROJECT_ID.us-central1.run.app' with:"
        echo "$SERVICE_URL"
        ;;
    
    "aws")
        echo "Deploying to AWS ECS..."
        # Build and push to ECR
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        AWS_REGION=${AWS_REGION:-us-east-1}
        ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
        
        # Login to ECR
        aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REGISTRY}
        
        # Create repository if it doesn't exist
        aws ecr describe-repositories --repository-names ${IMAGE_NAME} --region ${AWS_REGION} || \
        aws ecr create-repository --repository-name ${IMAGE_NAME} --region ${AWS_REGION}
        
        # Build and push
        docker build -t ${IMAGE_NAME}:${TAG} .
        docker tag ${IMAGE_NAME}:${TAG} ${ECR_REGISTRY}/${IMAGE_NAME}:${TAG}
        docker push ${ECR_REGISTRY}/${IMAGE_NAME}:${TAG}
        
        echo "Image pushed to ECR: ${ECR_REGISTRY}/${IMAGE_NAME}:${TAG}"
        echo "Update your ECS service to use this image"
        ;;
    
    "azure")
        echo "Deploying to Azure Container Instances..."
        RESOURCE_GROUP=${AZURE_RESOURCE_GROUP:-webrtc-backend-rg}
        REGISTRY_NAME=${AZURE_REGISTRY_NAME:-webrtcbackendregistry}
        
        # Build and push to ACR
        az acr build --registry ${REGISTRY_NAME} --image ${IMAGE_NAME}:${TAG} .
        
        # Deploy to Container Instances
        az container create \
            --resource-group ${RESOURCE_GROUP} \
            --name ${IMAGE_NAME} \
            --image ${REGISTRY_NAME}.azurecr.io/${IMAGE_NAME}:${TAG} \
            --dns-name-label ${IMAGE_NAME}-$(date +%s) \
            --ports 5055 \
            --environment-variables PORT=5055 ENVIRONMENT=production
        ;;
    
    *)
        echo "Unknown platform: ${PLATFORM}"
        echo "Supported platforms: docker, aws, gcp, azure"
        exit 1
        ;;
esac

echo "Deployment completed!"
