#!/usr/bin/env python3
"""
Simple test server to help test the WebRTC frontend with backend integration.
This script helps you start your backend service and provides testing utilities.
"""

import subprocess
import sys
import time
import requests
import json
from pathlib import Path

def check_backend_running(port=5055):
    """Check if the backend service is running on the specified port."""
    try:
        response = requests.get(f"http://localhost:{port}/", timeout=5)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def test_session_config_endpoint(org_id, port=5055):
    """Test the session config endpoint for a specific organisation."""
    try:
        url = f"http://localhost:{port}/api/session-config/{org_id}"
        print(f"Testing endpoint: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            config = response.json()
            print(f"✅ Success! Configuration found for {org_id}")
            print(f"   - Tools: {len(config.get('tools', []))}")
            print(f"   - Instructions length: {len(config.get('instructions', ''))}")
            print(f"   - API links: {len(config.get('api_links', {}))}")
            return True
        elif response.status_code == 404:
            print(f"❌ No configuration found for organisation: {org_id}")
            return False
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False

def start_backend_service():
    """Instructions to start the backend service."""
    print("🚀 To start your backend service, run one of these commands:")
    print()
    print("Option 1 - If you have a main.py in webRTC directory:")
    print("   cd ../webRTC && python main.py")
    print()
    print("Option 2 - If you have app.py in webRTC directory:")
    print("   cd ../webRTC && python app.py")
    print()
    print("Option 3 - If you're using uvicorn:")
    print("   cd ../webRTC && uvicorn app:app --host 0.0.0.0 --port 5055")
    print()
    print("The service should start on port 5055")

def main():
    print("🧪 WebRTC Frontend Test Helper")
    print("=" * 50)
    
    # Check if backend is running
    print("Checking if backend service is running on port 5055...")
    
    if check_backend_running(5055):
        print("✅ Backend service is running!")
        print()
        
        # Test some organisation IDs
        test_orgs = ['nayatel', 'test_org', 'Oladoc', 'demo_org']
        
        print("Testing organisation configurations:")
        print("-" * 40)
        
        for org_id in test_orgs:
            test_session_config_endpoint(org_id)
            print()
            
        print("🌐 You can now:")
        print("1. Open test.html in your browser to test different organisations")
        print("2. Open index.html with ?organisation_id=<org_id> to test WebRTC")
        print()
        print("Example URLs:")
        print("- http://localhost:8000/test.html")
        print("- http://localhost:8000/index.html?organisation_id=nayatel")
        
    else:
        print("❌ Backend service is not running on port 5055")
        print()
        start_backend_service()
        print()
        print("After starting the backend, run this script again to test.")

if __name__ == "__main__":
    main()
