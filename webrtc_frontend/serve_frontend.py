#!/usr/bin/env python3
"""
Simple HTTP server to serve the WebRTC frontend files.
This allows you to test the frontend locally with proper CORS handling.
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """HTTP request handler with CORS headers."""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    port = 8000
    
    # Change to the webrtc_frontend directory
    frontend_dir = Path(__file__).parent
    os.chdir(frontend_dir)
    
    print(f"🌐 Starting HTTP server on port {port}")
    print(f"📁 Serving files from: {frontend_dir}")
    print()
    print("Available URLs:")
    print(f"  - Test page: http://localhost:{port}/test.html")
    print(f"  - WebRTC frontend: http://localhost:{port}/index.html")
    print(f"  - With organisation: http://localhost:{port}/index.html?organisation_id=nayatel")
    print()
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")

if __name__ == "__main__":
    main()
