<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Test with Organisation ID</title>
    <script src="/config.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .primary { background-color: #007bff; color: white; }
        .secondary { background-color: #6c757d; color: white; }
        .success { background-color: #28a745; color: white; }
        .info { background-color: #17a2b8; color: white; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebRTC Test with Organisation Configuration</h1>
    
    <div class="test-section">
        <h3>Organisation ID Configuration</h3>
        <p>Test different organisation configurations:</p>
        
        <button class="primary" onclick="testWithOrganisation('nayatel')">
            Test with Nayatel Organisation
        </button>

        <button class="secondary" onclick="testWithOrganisation('test_org')">
            Test with Test Organisation
        </button>

        <button class="secondary" onclick="testWithOrganisation('Oladoc')">
            Test with Oladoc Organisation
        </button>

        <button class="secondary" onclick="testWithOrganisation('demo_org')">
            Test with Demo Organisation
        </button>

        <button class="info" onclick="testWithOrganisation(null)">
            Test with Default Configuration
        </button>
        
        <div style="margin: 10px 0;">
            <input type="text" id="customOrgId" placeholder="Enter custom organisation_id" style="padding: 8px; margin-right: 10px; width: 200px;">
            <button class="primary" onclick="testWithCustomOrganisation()">
                Test Custom Organisation
            </button>
        </div>

        <button class="success" onclick="clearLog()">
            Clear Log
        </button>

        <button class="info" onclick="testBasicConnection()">
            Test Basic Connection
        </button>

        <button class="secondary" onclick="testApiLinks()">
            Test API Links
        </button>
    </div>
    
    <div class="test-section">
        <h3>Current Configuration</h3>
        <p><strong>Organisation ID:</strong> <span id="currentOrg">None</span></p>
        <p><strong>Tools Count:</strong> <span id="toolsCount">0</span></p>
        <p><strong>Instructions Preview:</strong> <span id="instructionsPreview">None</span></p>
    </div>

    <div class="test-section">
        <h3>WebRTC Frontend Test</h3>
        <p>Test the actual WebRTC frontend with the selected organisation:</p>

        <button class="primary" onclick="openWebRTCWithCurrentOrg()">
            Open WebRTC Frontend with Current Org
        </button>

        <button class="secondary" onclick="openWebRTCInNewTab()">
            Open WebRTC in New Tab
        </button>

        <p><small>This will open the WebRTC frontend with the currently selected organisation_id</small></p>
    </div>
    
    <div class="test-section">
        <h3>Debug Log</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(orgId, toolsCount, instructions) {
            document.getElementById('currentOrg').textContent = orgId || 'None';
            document.getElementById('toolsCount').textContent = toolsCount || 0;
            document.getElementById('instructionsPreview').textContent =
                instructions ? instructions.substring(0, 100) + '...' : 'None';
        }

        function testWithCustomOrganisation() {
            const customOrgId = document.getElementById('customOrgId').value.trim();
            if (!customOrgId) {
                log('❌ Please enter a custom organisation ID');
                return;
            }
            testWithOrganisation(customOrgId);
        }

        function openWebRTCWithCurrentOrg() {
            const currentOrg = document.getElementById('currentOrg').textContent;
            if (currentOrg === 'None') {
                log('❌ Please select an organisation first');
                return;
            }

            const url = `index.html?organisation_id=${currentOrg}`;
            log(`🚀 Opening WebRTC frontend with organisation: ${currentOrg}`);
            window.open(url, '_blank');
        }

        function openWebRTCInNewTab() {
            const url = 'index.html';
            log('🚀 Opening WebRTC frontend with default configuration');
            window.open(url, '_blank');
        }

        async function testBasicConnection() {
            log('Testing basic connection to backend...');
            const baseUrl = "http://localhost:5055";

            try {
                // Test basic endpoint first
                log(`Testing: ${baseUrl}/`);
                const response = await fetch(`${baseUrl}/`);

                if (response.ok) {
                    const data = await response.text();
                    log(`✅ Basic connection successful: ${data}`);
                } else {
                    log(`❌ Basic connection failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`❌ Connection error: ${error.message}`);
                log('💡 Make sure your backend service is running on port 5055');
                log('💡 Try running: cd ../webRTC && python app.py');
            }
        }

        async function testApiLinks() {
            log('Testing API links functionality...');
            const baseUrl = window.WebRTCConfig ? window.WebRTCConfig.backendUrl : "http://localhost:5055";

            try {
                // First get the session config to load API links
                const orgId = 'nayatel'; // Test with nayatel
                log(`Fetching session config for ${orgId}...`);

                const response = await fetch(`${baseUrl}/api/session-config/${orgId}`);
                if (!response.ok) {
                    throw new Error(`Failed to fetch session config: ${response.status}`);
                }

                const config = await response.json();

                if (config.api_links && Object.keys(config.api_links).length > 0) {
                    log(`✅ Found ${Object.keys(config.api_links).length} API links:`);

                    // Display available API endpoints
                    Object.entries(config.api_links).forEach(([functionName, [url, method]]) => {
                        log(`  - ${functionName}: ${method} ${url}`);
                    });

                    // Store API links globally (simulate what happens in WebRTC)
                    window.apiLinks = config.api_links;
                    log('✅ API links stored globally for testing');

                } else {
                    log('❌ No API links found in configuration');
                }

            } catch (error) {
                log(`❌ Error testing API links: ${error.message}`);
            }
        }

        async function testWithOrganisation(orgId) {
            log(`Testing with organisation: ${orgId || 'default'}`);
            
            try {
                // Store in localStorage
                if (orgId) {
                    localStorage.setItem('organisation_id', orgId);
                } else {
                    localStorage.removeItem('organisation_id');
                }
                
                // Test the API endpoint directly
                const baseUrl = window.WebRTCConfig ? window.WebRTCConfig.backendUrl : "http://localhost:5055";
                
                if (orgId) {
                    log(`Fetching session config from: ${baseUrl}/api/session-config/${orgId}`);
                    const response = await fetch(`${baseUrl}/api/session-config/${orgId}`);
                    
                    if (response.ok) {
                        const config = await response.json();
                        log(`✅ Successfully fetched config:`);
                        log(`   - Tools: ${config.tools.length}`);
                        log(`   - Instructions length: ${config.instructions.length}`);
                        log(`   - API links: ${Object.keys(config.api_links || {}).length}`);
                        
                        updateStatus(orgId, config.tools.length, config.instructions);
                    } else {
                        log(`❌ Failed to fetch config: ${response.status} ${response.statusText}`);
                        updateStatus(orgId, 0, null);
                    }
                } else {
                    log(`Using default configuration`);
                    updateStatus(null, 0, 'Default instructions');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                updateStatus(orgId, 0, null);
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('Test page loaded');
            const storedOrgId = localStorage.getItem('organisation_id');
            if (storedOrgId) {
                log(`Found stored organisation_id: ${storedOrgId}`);
                testWithOrganisation(storedOrgId);
            }
        });
    </script>
</body>
</html>
