# WebRTC Frontend Testing Guide

This guide helps you test your WebRTC frontend with different organisation IDs and backend configurations.

## Prerequisites

1. **Backend Service**: Your backend service should be running on port 5055
2. **Database**: Your PostgreSQL database should be accessible with organisation configurations
3. **Frontend Files**: All frontend files should be in the `webrtc_frontend` directory

## Quick Start

### 1. Start Backend Service

Navigate to your backend directory and start the service:

```bash
# Option 1: If using main.py
cd ../webRTC
python main.py

# Option 2: If using app.py
cd ../webRTC
python app.py

# Option 3: If using uvicorn
cd ../webRTC
uvicorn app:app --host 0.0.0.0 --port 5055
```

### 2. Start Frontend Server

In a new terminal, start the frontend server:

```bash
cd webrtc_frontend
python serve_frontend.py
```

This will start an HTTP server on port 8000 with CORS enabled.

### 3. Test Backend Connection

Run the test helper to verify your backend is working:

```bash
python start_test_server.py
```

## Testing Different Organisation IDs

### Using the Test Interface

1. Open your browser and go to: `http://localhost:8000/test.html`

2. The test page provides buttons to test different organisations:
   - **Nayatel Organisation**: Tests with `organisation_id=nayatel`
   - **Test Organisation**: Tests with `organisation_id=test_org`
   - **Oladoc Organisation**: Tests with `organisation_id=Oladoc`
   - **Demo Organisation**: Tests with `organisation_id=demo_org`
   - **Default Configuration**: Tests without organisation_id (uses default tools)

3. You can also enter a custom organisation ID in the input field

4. The test page will show:
   - Current organisation ID
   - Number of tools loaded
   - Preview of instructions
   - Debug log with API responses

### Using the WebRTC Frontend Directly

1. **With Organisation ID**: 
   ```
   http://localhost:8000/index.html?organisation_id=nayatel
   ```

2. **Default Configuration**:
   ```
   http://localhost:8000/index.html
   ```

## What Each Test Does

### Backend API Test
- Calls `GET /api/session-config/{organisation_id}`
- Displays the response including tools, instructions, and API links
- Shows any errors or missing configurations

### WebRTC Frontend Test
- Loads the organisation configuration when WebRTC starts
- Configures the AI model with the appropriate tools and instructions
- Stores API links for function calling

## Expected Behavior

### Successful Configuration Load
```
✅ Successfully fetched config:
   - Tools: 15
   - Instructions length: 1250
   - API links: 3
```

### Missing Configuration
```
❌ Failed to fetch config: 404 Not Found
```

### Default Fallback
When no organisation_id is provided or the configuration fails to load, the frontend will use the default hardcoded tools from `getDefaultTools()` function.

## Troubleshooting

### Backend Not Running
- Error: Connection refused on port 5055
- Solution: Start your backend service first

### Database Connection Issues
- Error: Database connection failed
- Solution: Check your database credentials and connection

### CORS Issues
- Error: CORS policy blocked
- Solution: Use the provided `serve_frontend.py` server instead of opening files directly

### Organisation Not Found
- Error: 404 Not Found for organisation
- Solution: Check if the organisation exists in your database

## Database Setup

Make sure your database has the required tables:

```sql
-- Example organisation
INSERT INTO chatbot.organisations (organisation_id, inbound_secret) 
VALUES ('nayatel', 'your_secret_here');

-- Example function definition
INSERT INTO chatbot.function_definitions 
(organisation_id, function_name, function_schema, system_prompt, api_link, method_type)
VALUES 
('nayatel', 'get_order_status', '{"type": "function", ...}', 'You are a helpful assistant...', 'http://localhost:5055/api/orders', 'GET');
```

## Testing Checklist

- [ ] Backend service running on port 5055
- [ ] Frontend server running on port 8000
- [ ] Database accessible and populated
- [ ] Test page loads without errors
- [ ] Can fetch configurations for different organisations
- [ ] WebRTC frontend loads with organisation-specific tools
- [ ] Function calling works with organisation-specific endpoints
- [ ] Default configuration works when no organisation is specified

## Next Steps

After successful testing:
1. Deploy your backend service to production
2. Update the `baseUrl` in `script.js` to point to your production backend
3. Deploy your frontend to your preferred hosting platform
4. Test with real organisation IDs in production
