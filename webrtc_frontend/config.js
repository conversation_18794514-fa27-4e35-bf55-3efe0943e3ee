// Configuration for WebRTC Frontend
// This file determines the backend URL based on the environment

// Function to determine the backend URL
function getBackendUrl() {
    // Check if we're running in Docker (backend service name)
    if (window.location.hostname === 'localhost' && window.location.port === '8080') {
        // Running in Docker Compose - use service name
        return 'http://localhost:5055';
    }
    
    // Check for environment-specific configuration
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        // Local development
        return 'http://localhost:5055';
    } else {
        // Production - assume backend is on same domain with different port or path
        return `${window.location.protocol}//${hostname}:5055`;
    }
}

// Export the configuration
window.WebRTCConfig = {
    backendUrl: getBackendUrl(),
    // Add other configuration options here
    debug: window.location.hostname === 'localhost',
    retryAttempts: 3,
    retryDelay: 1000
};

// Log configuration for debugging
if (window.WebRTCConfig.debug) {
    console.log('WebRTC Configuration:', window.WebRTCConfig);
}
