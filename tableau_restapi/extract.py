import requests
import json
import time

# --- 1. CONFIGURATION: Load from environment variables ---
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

TABLEAU_URL = os.getenv("TABLEAU_URL")
API_VERSION = os.getenv("API_VERSION")
PAT_NAME = os.getenv("PAT_NAME")
PAT_SECRET = os.getenv("PAT_SECRET")
DATASOURCE_ID = os.getenv("DATASOURCE_ID")
SITE_NAME = os.getenv("SITE_NAME")

# Validate that all required environment variables are set
required_vars = ["TABLEAU_URL", "API_VERSION", "PAT_NAME", "PAT_SECRET", "DATASOURCE_ID", "SITE_NAME"]
missing_vars = [var for var in required_vars if not os.getenv(var)]

if missing_vars:
    raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

print(f"Configuration loaded for site: {SITE_NAME}")

# --- 2. THE SCRIPT LOGIC WRAPPED IN A FUNCTION ---

def refresh_tableau_extract():
    """
    Signs in to Tableau, triggers an extract refresh, and signs out.
    """
    print(f"--- Running scheduled refresh at {time.ctime()} ---")
    
    # Construct the base URL for API calls
    base_url = f"{TABLEAU_URL}/api/{API_VERSION}"

    # Step 1: Sign In to Tableau Cloud
    signin_url = f"{base_url}/auth/signin"
    signin_payload = {
        "credentials": {
            "personalAccessTokenName": PAT_NAME,
            "personalAccessTokenSecret": PAT_SECRET,
            "site": {
                "contentUrl": SITE_NAME
            }
        }
    }
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    auth_token = None # Initialize auth_token to ensure it exists for the 'finally' block
    try:
        print("Attempting to sign in...")
        req = requests.post(signin_url, json=signin_payload, headers=headers, timeout=30)
        req.raise_for_status()
        response_json = req.json()
        
        auth_token = response_json['credentials']['token']
        site_id = response_json['credentials']['site']['id']
        
        print("Sign in successful!")

        # Step 2: Trigger the Extract Refresh
        refresh_url = f"{base_url}/sites/{site_id}/datasources/{DATASOURCE_ID}/refresh"
        refresh_headers = {
            "X-Tableau-Auth": auth_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        print(f"Requesting refresh for data source {DATASOURCE_ID}...")
        refresh_req = requests.post(refresh_url, headers=refresh_headers, json={}, timeout=30)
        refresh_req.raise_for_status()
        job_info = refresh_req.json()
        
        print("Extract refresh job started successfully!")
        print(f"Job ID: {job_info['job']['id']}")

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        if e.response:
            print(f"Response Body: {e.response.text}")

    finally:
        # Step 3: Sign Out
        if auth_token:
            print("Signing out...")
            signout_url = f"{base_url}/auth/signout"
            signout_headers = {"X-Tableau-Auth": auth_token}
            requests.post(signout_url, headers=signout_headers, timeout=30)
            print("Sign out complete.")
            print("-" * 20)

# --- 3. SCHEDULER SETUP ---
if __name__ == "__main__":
    # Schedule the job to run every 5 minutes
    
    print("Scheduler started. Waiting for the first run...")
    
    # Run the job immediately once at the start
    refresh_tableau_extract()
    