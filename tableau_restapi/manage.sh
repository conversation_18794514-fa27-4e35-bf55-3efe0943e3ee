#!/bin/bash

# Tableau Extract Job Management Script

set -e

# Configuration
JOB_NAME="tableau-extract-job"
SCHEDULER_NAME="tableau-extract-scheduler"
REGION="us-central1"
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo "Usage: $0 {deploy|run|logs|status|pause|resume|delete}"
    echo ""
    echo "Commands:"
    echo "  deploy  - Deploy the job and scheduler to Cloud Run"
    echo "  run     - Execute the job manually"
    echo "  logs    - View recent job logs"
    echo "  status  - Check job and scheduler status"
    echo "  pause   - Pause the scheduler"
    echo "  resume  - Resume the scheduler"
    echo "  delete  - Delete the job and scheduler"
}

case "$1" in
    deploy)
        echo -e "${BLUE}🚀 Deploying Tableau Extract Job...${NC}"
        ./deploy.sh
        ;;
    
    run)
        echo -e "${BLUE}🏃 Running job manually...${NC}"
        gcloud run jobs execute ${JOB_NAME} --region=${REGION} --project=${PROJECT_ID} --wait
        ;;
    
    logs)
        echo -e "${BLUE}📋 Fetching recent logs...${NC}"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=${JOB_NAME}" \
            --limit=50 \
            --format='table(timestamp,textPayload)' \
            --project=${PROJECT_ID}
        ;;
    
    status)
        echo -e "${BLUE}📊 Checking status...${NC}"
        echo ""
        echo -e "${YELLOW}Job Status:${NC}"
        gcloud run jobs describe ${JOB_NAME} --region=${REGION} --project=${PROJECT_ID} \
            --format='table(metadata.name,spec.template.spec.template.spec.containers[0].image,status.conditions[0].type,status.conditions[0].status)' || echo "Job not found"
        
        echo ""
        echo -e "${YELLOW}Scheduler Status:${NC}"
        gcloud scheduler jobs describe ${SCHEDULER_NAME} --location=${REGION} --project=${PROJECT_ID} \
            --format='table(name,schedule,state,lastAttemptTime,nextRunTime)' || echo "Scheduler not found"
        ;;
    
    pause)
        echo -e "${YELLOW}⏸️  Pausing scheduler...${NC}"
        gcloud scheduler jobs pause ${SCHEDULER_NAME} --location=${REGION} --project=${PROJECT_ID}
        echo -e "${GREEN}✅ Scheduler paused${NC}"
        ;;
    
    resume)
        echo -e "${GREEN}▶️  Resuming scheduler...${NC}"
        gcloud scheduler jobs resume ${SCHEDULER_NAME} --location=${REGION} --project=${PROJECT_ID}
        echo -e "${GREEN}✅ Scheduler resumed${NC}"
        ;;
    
    delete)
        echo -e "${RED}🗑️  Deleting job and scheduler...${NC}"
        read -p "Are you sure you want to delete the job and scheduler? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "Deleting scheduler..."
            gcloud scheduler jobs delete ${SCHEDULER_NAME} --location=${REGION} --project=${PROJECT_ID} --quiet || echo "Scheduler not found"
            
            echo "Deleting job..."
            gcloud run jobs delete ${JOB_NAME} --region=${REGION} --project=${PROJECT_ID} --quiet || echo "Job not found"
            
            echo -e "${GREEN}✅ Cleanup completed${NC}"
        else
            echo "Cancelled"
        fi
        ;;
    
    *)
        print_usage
        exit 1
        ;;
esac
