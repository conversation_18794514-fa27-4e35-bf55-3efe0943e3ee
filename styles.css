/* Basic reset and off-white background */
body {
    margin: 0;
    font-family: Arial, sans-serif;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Blue top banner */
header {
    background-color: #1976d2;
    color: white;
    padding: 20px;
    position: relative;
}

header h1 {
    margin: 0;
    font-size: 24px;
}

/* "Powered by Quicktalk" top right label */
header .powered-by {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 14px;
    font-style: italic;
    opacity: 0.9;
}

/* Main content styles */
.content {
    flex: 1;
    padding: 20px;
    text-align: center;
}

.content p {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
}

button {
    padding: 12px 24px;
    font-size: 16px;
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #1565c0;
}

/* Sticky footer */
footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 10px;
    position: sticky;
    bottom: 0;
}
