# Testing and Development Files
test.html
TESTING_GUIDE.md
serve_frontend.py
start_test_server.py
*.test.js
*.spec.js
test/
tests/
__tests__/

# Environment Files
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# Development Servers and Scripts
serve_*.py
start_*.py
dev_server.*
local_server.*

# Build Artifacts
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist

# Node.js Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package Manager Files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Runtime Data
pids
*.pid
*.seed
*.pid.lock

# Coverage Directory
coverage/
*.lcov
.nyc_output

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary Files
tmp/
temp/
.tmp/
.temp/

# Cache Directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Optional npm Cache Directory
.npm

# Optional REPL History
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity File
.yarn-integrity

# dotenv Environment Variables File
.env.backup

# Microbundle Cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL History
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity File
.yarn-integrity

# parcel-bundler Cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js Build Output
.next

# Nuxt.js Build / Generate Output
.nuxt
dist

# Storybook Build Outputs
.out
.storybook-out

# Temporary Folders
tmp/
temp/

# Editor Directories and Files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# Windows Thumbnail Cache Files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump File
*.stackdump

# Folder Config File
[Dd]esktop.ini

# Recycle Bin Used on File Shares
$RECYCLE.BIN/

# Windows Installer Files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows Shortcuts
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~

# Temporary Files Created by Most Text Editors and IDEs
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# External Tool Builders
.externalToolBuilders/

# Locally Stored "Eclipse Launch Configurations"
*.launch

# PyDev Specific (Python IDE for Eclipse)
*.pydevproject

# CDT-specific (C/C++ Development Tooling)
.cproject

# CDT- autotools
.autotools

# Java annotation processor (APT)
.factorypath

# PDT-specific (PHP Development Tools)
.buildpath

# sbteclipse plugin
.target

# Tern plugin
.tern-project

# TeXlipse plugin
.texlipse

# STS (Spring Tool Suite)
.springBeans

# Code Recommenders
.recommenders/

# Annotation Processing
.apt_generated/
.apt_generated_test/

# Scala IDE specific (Scala & Java IDE for Eclipse)
.cache-main
.scala_dependencies
.worksheet

# Uncomment this line if you wish to ignore the project description file.
# Typically, this file would be tracked if it contains build/dependency configurations:
#.project
