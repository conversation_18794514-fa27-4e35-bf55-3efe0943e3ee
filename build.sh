#!/bin/bash

# Build script for WebRTC Frontend Docker image

set -e

# Configuration
IMAGE_NAME="webrtc-frontend"
TAG=${1:-latest}
REGISTRY=${2:-""}

echo "Building WebRTC Frontend Docker image..."
echo "Image: ${IMAGE_NAME}:${TAG}"

# Build the Docker image
docker build -t ${IMAGE_NAME}:${TAG} .

# Tag for registry if provided
if [ ! -z "$REGISTRY" ]; then
    echo "Tagging for registry: ${REGISTRY}"
    docker tag ${IMAGE_NAME}:${TAG} ${REGISTRY}/${IMAGE_NAME}:${TAG}
    
    echo "Pushing to registry..."
    docker push ${REGISTRY}/${IMAGE_NAME}:${TAG}
fi

echo "Build completed successfully!"
echo ""
echo "To run locally:"
echo "  docker run -p 8080:80 ${IMAGE_NAME}:${TAG}"
echo ""
echo "To run with docker-compose:"
echo "  docker-compose up -d"
echo ""
echo "Access the application at: http://localhost:8080"
