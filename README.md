# WebRTC Frontend - Docker Deployment

A containerized WebRTC frontend application with order tracking functionality.

## Features

- WebRTC audio communication
- Function calling with order status tracking
- 25+ tracking numbers with detailed information
- Responsive web interface
- Docker containerization for easy deployment

## Quick Start

### Local Development

1. **Build the Docker image:**
   ```bash
   ./build.sh
   ```

2. **Run with Docker:**
   ```bash
   docker run -p 8080:80 webrtc-frontend:latest
   ```

3. **Or run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

4. **Access the application:**
   Open http://localhost:8080 in your browser

### Cloud Deployment

#### AWS ECS/ECR
```bash
export AWS_REGION=us-east-1
./deploy.sh latest aws
```

#### Google Cloud Run
```bash
export GCP_PROJECT_ID=your-project-id
./deploy.sh latest gcp
```

#### Azure Container Instances
```bash
export AZURE_RESOURCE_GROUP=webrtc-frontend-rg
export AZURE_REGISTRY_NAME=webrtcfrontendregistry
./deploy.sh latest azure
```

#### Heroku
```bash
export HEROKU_APP_NAME=your-app-name
./deploy.sh latest heroku
```

## Configuration

### Environment Variables

- `NGINX_HOST`: Nginx host (default: localhost)
- `NGINX_PORT`: Nginx port (default: 80)

### Docker Compose Override

Create `docker-compose.override.yml` for custom configuration:

```yaml
version: '3.8'
services:
  webrtc-frontend:
    ports:
      - "3000:80"  # Custom port
    environment:
      - CUSTOM_VAR=value
```

## Health Check

The application includes a health check endpoint at `/health` that returns "healthy" when the service is running properly.

## Security

The Nginx configuration includes:
- Security headers (X-Frame-Options, X-XSS-Protection, etc.)
- Content Security Policy
- Gzip compression
- Static asset caching

## Monitoring

### Docker Health Check
```bash
docker ps  # Check container health status
```

### Application Logs
```bash
docker logs webrtc-frontend
```

### Nginx Access Logs
```bash
docker exec webrtc-frontend tail -f /var/log/nginx/access.log
```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   docker-compose down
   # Or change port in docker-compose.yml
   ```

2. **Build fails:**
   ```bash
   docker system prune -f
   ./build.sh
   ```

3. **Container won't start:**
   ```bash
   docker logs webrtc-frontend
   ```

### Debug Mode

Run container with debug output:
```bash
docker run -p 8080:80 -e DEBUG=true webrtc-frontend:latest
```

## File Structure

```
webrtc_frontend/
├── Dockerfile              # Docker build configuration
├── docker-compose.yml      # Docker Compose configuration
├── nginx.conf              # Nginx server configuration
├── build.sh                # Build script
├── deploy.sh               # Deployment script
├── index.html              # Main HTML file
├── script.js               # JavaScript with WebRTC logic
├── styles.css              # CSS styles
└── README.md               # This file
```

## Order Tracking Function

The application includes a comprehensive order tracking system with 25+ tracking numbers:

- Real-time order status updates
- Multiple carriers (FedEx, UPS, USPS, DHL)
- Detailed delivery information
- Exception handling for delivery issues

## Contributing

1. Make changes to the source files
2. Test locally with `docker-compose up`
3. Build new image with `./build.sh`
4. Deploy with `./deploy.sh`

## License

MIT License - see LICENSE file for details
