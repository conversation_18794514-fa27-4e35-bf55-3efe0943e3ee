<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Performance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .primary { background-color: #007bff; color: white; }
        .secondary { background-color: #6c757d; color: white; }
        .success { background-color: #28a745; color: white; }
        .warning { background-color: #ffc107; color: black; }
        #results {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .fast { color: #28a745; font-weight: bold; }
        .slow { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <h1>API Performance Test</h1>
    
    <div class="test-section">
        <h3>Test API Call Performance</h3>
        <p>Compare direct API calls vs proxied calls through backend:</p>
        
        <button class="primary" onclick="testDirectCall()">
            Test Direct API Call
        </button>
        
        <button class="secondary" onclick="testProxiedCall()">
            Test Proxied API Call
        </button>
        
        <button class="success" onclick="testBothMethods()">
            Test Both Methods (5x each)
        </button>
        
        <button class="warning" onclick="clearResults()">
            Clear Results
        </button>
    </div>
    
    <div class="test-section">
        <h3>Performance Results</h3>
        <div id="results"></div>
    </div>

    <script>
        const baseUrl = "http://localhost:5055";
        const testApiUrl = "https://nayatelbeprod.nayatel.com/city/getCities";
        
        function log(message, className = '') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = className;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testDirectCall() {
            log('🚀 Testing direct API call...');
            const startTime = performance.now();
            
            try {
                const response = await fetch(testApiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Direct call successful: ${duration.toFixed(2)}ms`, 'fast');
                    log(`   Cities returned: ${data.data?.length || 'unknown'}`);
                } else {
                    log(`❌ Direct call failed: ${response.status} ${response.statusText}`, 'slow');
                }
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                log(`❌ Direct call error (${duration.toFixed(2)}ms): ${error.message}`, 'slow');
                
                if (error.message.includes('CORS')) {
                    log('   💡 CORS error - this is expected for cross-origin requests');
                }
            }
        }

        async function testProxiedCall() {
            log('🔄 Testing proxied API call...');
            const startTime = performance.now();
            
            try {
                const response = await fetch(`${baseUrl}/api/function-call/nayatel/get_cities`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({})
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Proxied call successful: ${duration.toFixed(2)}ms`, duration < 500 ? 'fast' : 'slow');
                    log(`   Cities returned: ${data.data?.length || 'unknown'}`);
                } else {
                    log(`❌ Proxied call failed: ${response.status} ${response.statusText}`, 'slow');
                }
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                log(`❌ Proxied call error (${duration.toFixed(2)}ms): ${error.message}`, 'slow');
            }
        }

        async function testBothMethods() {
            log('🧪 Running performance comparison (5 tests each)...');
            
            const directTimes = [];
            const proxiedTimes = [];
            
            // Test direct calls
            for (let i = 1; i <= 5; i++) {
                log(`Direct test ${i}/5...`);
                const startTime = performance.now();
                
                try {
                    await fetch(testApiUrl, { method: 'GET' });
                    const duration = performance.now() - startTime;
                    directTimes.push(duration);
                    log(`  Direct ${i}: ${duration.toFixed(2)}ms`);
                } catch (error) {
                    const duration = performance.now() - startTime;
                    directTimes.push(duration);
                    log(`  Direct ${i}: ${duration.toFixed(2)}ms (CORS error expected)`);
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // Test proxied calls
            for (let i = 1; i <= 5; i++) {
                log(`Proxied test ${i}/5...`);
                const startTime = performance.now();
                
                try {
                    await fetch(`${baseUrl}/api/function-call/nayatel/get_cities`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({})
                    });
                    const duration = performance.now() - startTime;
                    proxiedTimes.push(duration);
                    log(`  Proxied ${i}: ${duration.toFixed(2)}ms`);
                } catch (error) {
                    const duration = performance.now() - startTime;
                    proxiedTimes.push(duration);
                    log(`  Proxied ${i}: ${duration.toFixed(2)}ms (error)`);
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // Calculate averages
            const avgDirect = directTimes.reduce((a, b) => a + b, 0) / directTimes.length;
            const avgProxied = proxiedTimes.reduce((a, b) => a + b, 0) / proxiedTimes.length;
            const difference = avgProxied - avgDirect;
            
            log('📊 PERFORMANCE SUMMARY:', 'fast');
            log(`   Direct calls average: ${avgDirect.toFixed(2)}ms`, 'fast');
            log(`   Proxied calls average: ${avgProxied.toFixed(2)}ms`, avgProxied < 500 ? 'fast' : 'slow');
            log(`   Difference: +${difference.toFixed(2)}ms (${((difference/avgDirect)*100).toFixed(1)}% slower)`, difference > 200 ? 'slow' : 'warning');
            
            if (difference > 200) {
                log('💡 Recommendation: Use direct calls when possible for better performance', 'warning');
            }
        }
    </script>
</body>
</html>
