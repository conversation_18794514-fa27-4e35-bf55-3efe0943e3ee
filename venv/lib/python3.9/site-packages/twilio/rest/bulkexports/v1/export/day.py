r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Bulkexports
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class DayInstance(InstanceResource):
    """
    :ivar redirect_to:
    :ivar day: The ISO 8601 format date of the resources in the file, for a UTC day
    :ivar size: The size of the day's data file in bytes
    :ivar create_date: The ISO 8601 format date when resources is created
    :ivar friendly_name: The friendly name specified when creating the job
    :ivar resource_type: The type of communication – Messages, Calls, Conferences, and Participants
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        resource_type: str,
        day: Optional[str] = None,
    ):
        super().__init__(version)

        self.redirect_to: Optional[str] = payload.get("redirect_to")
        self.day: Optional[str] = payload.get("day")
        self.size: Optional[int] = deserialize.integer(payload.get("size"))
        self.create_date: Optional[str] = payload.get("create_date")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.resource_type: Optional[str] = payload.get("resource_type")

        self._solution = {
            "resource_type": resource_type,
            "day": day or self.day,
        }
        self._context: Optional[DayContext] = None

    @property
    def _proxy(self) -> "DayContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: DayContext for this DayInstance
        """
        if self._context is None:
            self._context = DayContext(
                self._version,
                resource_type=self._solution["resource_type"],
                day=self._solution["day"],
            )
        return self._context

    def fetch(self) -> "DayInstance":
        """
        Fetch the DayInstance


        :returns: The fetched DayInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "DayInstance":
        """
        Asynchronous coroutine to fetch the DayInstance


        :returns: The fetched DayInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Bulkexports.V1.DayInstance {}>".format(context)


class DayContext(InstanceContext):

    def __init__(self, version: Version, resource_type: str, day: str):
        """
        Initialize the DayContext

        :param version: Version that contains the resource
        :param resource_type: The type of communication – Messages, Calls, Conferences, and Participants
        :param day: The ISO 8601 format date of the resources in the file, for a UTC day
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "resource_type": resource_type,
            "day": day,
        }
        self._uri = "/Exports/{resource_type}/Days/{day}".format(**self._solution)

    def fetch(self) -> DayInstance:
        """
        Fetch the DayInstance


        :returns: The fetched DayInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return DayInstance(
            self._version,
            payload,
            resource_type=self._solution["resource_type"],
            day=self._solution["day"],
        )

    async def fetch_async(self) -> DayInstance:
        """
        Asynchronous coroutine to fetch the DayInstance


        :returns: The fetched DayInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return DayInstance(
            self._version,
            payload,
            resource_type=self._solution["resource_type"],
            day=self._solution["day"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Bulkexports.V1.DayContext {}>".format(context)


class DayPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> DayInstance:
        """
        Build an instance of DayInstance

        :param payload: Payload response from the API
        """
        return DayInstance(
            self._version, payload, resource_type=self._solution["resource_type"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Bulkexports.V1.DayPage>"


class DayList(ListResource):

    def __init__(self, version: Version, resource_type: str):
        """
        Initialize the DayList

        :param version: Version that contains the resource
        :param resource_type: The type of communication – Messages, Calls, Conferences, and Participants

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "resource_type": resource_type,
        }
        self._uri = "/Exports/{resource_type}/Days".format(**self._solution)

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[DayInstance]:
        """
        Streams DayInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[DayInstance]:
        """
        Asynchronously streams DayInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[DayInstance]:
        """
        Lists DayInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[DayInstance]:
        """
        Asynchronously lists DayInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> DayPage:
        """
        Retrieve a single page of DayInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of DayInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return DayPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> DayPage:
        """
        Asynchronously retrieve a single page of DayInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of DayInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return DayPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> DayPage:
        """
        Retrieve a specific page of DayInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of DayInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return DayPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> DayPage:
        """
        Asynchronously retrieve a specific page of DayInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of DayInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return DayPage(self._version, response, self._solution)

    def get(self, day: str) -> DayContext:
        """
        Constructs a DayContext

        :param day: The ISO 8601 format date of the resources in the file, for a UTC day
        """
        return DayContext(
            self._version, resource_type=self._solution["resource_type"], day=day
        )

    def __call__(self, day: str) -> DayContext:
        """
        Constructs a DayContext

        :param day: The ISO 8601 format date of the resources in the file, for a UTC day
        """
        return DayContext(
            self._version, resource_type=self._solution["resource_type"], day=day
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Bulkexports.V1.DayList>"
