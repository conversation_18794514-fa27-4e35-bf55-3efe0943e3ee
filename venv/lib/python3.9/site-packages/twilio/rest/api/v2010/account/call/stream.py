r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class StreamInstance(InstanceResource):

    class Status(object):
        IN_PROGRESS = "in-progress"
        STOPPED = "stopped"

    class Track(object):
        INBOUND_TRACK = "inbound_track"
        OUTBOUND_TRACK = "outbound_track"
        BOTH_TRACKS = "both_tracks"

    class UpdateStatus(object):
        STOPPED = "stopped"

    """
    :ivar sid: The SID of the Stream resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created this Stream resource.
    :ivar call_sid: The SID of the [Call](https://www.twilio.com/docs/voice/api/call-resource) the Stream resource is associated with.
    :ivar name: The user-specified name of this Stream, if one was given when the Stream was created. This can be used to stop the Stream.
    :ivar status: 
    :ivar date_updated: The date and time in GMT that this resource was last updated, specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        call_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.call_sid: Optional[str] = payload.get("call_sid")
        self.name: Optional[str] = payload.get("name")
        self.status: Optional["StreamInstance.Status"] = payload.get("status")
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.uri: Optional[str] = payload.get("uri")

        self._solution = {
            "account_sid": account_sid,
            "call_sid": call_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[StreamContext] = None

    @property
    def _proxy(self) -> "StreamContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: StreamContext for this StreamInstance
        """
        if self._context is None:
            self._context = StreamContext(
                self._version,
                account_sid=self._solution["account_sid"],
                call_sid=self._solution["call_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def update(self, status: "StreamInstance.UpdateStatus") -> "StreamInstance":
        """
        Update the StreamInstance

        :param status:

        :returns: The updated StreamInstance
        """
        return self._proxy.update(
            status=status,
        )

    async def update_async(
        self, status: "StreamInstance.UpdateStatus"
    ) -> "StreamInstance":
        """
        Asynchronous coroutine to update the StreamInstance

        :param status:

        :returns: The updated StreamInstance
        """
        return await self._proxy.update_async(
            status=status,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.StreamInstance {}>".format(context)


class StreamContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, call_sid: str, sid: str):
        """
        Initialize the StreamContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created this Stream resource.
        :param call_sid: The SID of the [Call](https://www.twilio.com/docs/voice/api/call-resource) the Stream resource is associated with.
        :param sid: The SID or the `name` of the Stream resource to be stopped
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "call_sid": call_sid,
            "sid": sid,
        }
        self._uri = (
            "/Accounts/{account_sid}/Calls/{call_sid}/Streams/{sid}.json".format(
                **self._solution
            )
        )

    def update(self, status: "StreamInstance.UpdateStatus") -> StreamInstance:
        """
        Update the StreamInstance

        :param status:

        :returns: The updated StreamInstance
        """

        data = values.of(
            {
                "Status": status,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return StreamInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            call_sid=self._solution["call_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self, status: "StreamInstance.UpdateStatus"
    ) -> StreamInstance:
        """
        Asynchronous coroutine to update the StreamInstance

        :param status:

        :returns: The updated StreamInstance
        """

        data = values.of(
            {
                "Status": status,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return StreamInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            call_sid=self._solution["call_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.StreamContext {}>".format(context)


class StreamList(ListResource):

    def __init__(self, version: Version, account_sid: str, call_sid: str):
        """
        Initialize the StreamList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created this Stream resource.
        :param call_sid: The SID of the [Call](https://www.twilio.com/docs/voice/api/call-resource) the Stream resource is associated with.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "call_sid": call_sid,
        }
        self._uri = "/Accounts/{account_sid}/Calls/{call_sid}/Streams.json".format(
            **self._solution
        )

    def create(
        self,
        url: str,
        name: Union[str, object] = values.unset,
        track: Union["StreamInstance.Track", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        parameter1_name: Union[str, object] = values.unset,
        parameter1_value: Union[str, object] = values.unset,
        parameter2_name: Union[str, object] = values.unset,
        parameter2_value: Union[str, object] = values.unset,
        parameter3_name: Union[str, object] = values.unset,
        parameter3_value: Union[str, object] = values.unset,
        parameter4_name: Union[str, object] = values.unset,
        parameter4_value: Union[str, object] = values.unset,
        parameter5_name: Union[str, object] = values.unset,
        parameter5_value: Union[str, object] = values.unset,
        parameter6_name: Union[str, object] = values.unset,
        parameter6_value: Union[str, object] = values.unset,
        parameter7_name: Union[str, object] = values.unset,
        parameter7_value: Union[str, object] = values.unset,
        parameter8_name: Union[str, object] = values.unset,
        parameter8_value: Union[str, object] = values.unset,
        parameter9_name: Union[str, object] = values.unset,
        parameter9_value: Union[str, object] = values.unset,
        parameter10_name: Union[str, object] = values.unset,
        parameter10_value: Union[str, object] = values.unset,
        parameter11_name: Union[str, object] = values.unset,
        parameter11_value: Union[str, object] = values.unset,
        parameter12_name: Union[str, object] = values.unset,
        parameter12_value: Union[str, object] = values.unset,
        parameter13_name: Union[str, object] = values.unset,
        parameter13_value: Union[str, object] = values.unset,
        parameter14_name: Union[str, object] = values.unset,
        parameter14_value: Union[str, object] = values.unset,
        parameter15_name: Union[str, object] = values.unset,
        parameter15_value: Union[str, object] = values.unset,
        parameter16_name: Union[str, object] = values.unset,
        parameter16_value: Union[str, object] = values.unset,
        parameter17_name: Union[str, object] = values.unset,
        parameter17_value: Union[str, object] = values.unset,
        parameter18_name: Union[str, object] = values.unset,
        parameter18_value: Union[str, object] = values.unset,
        parameter19_name: Union[str, object] = values.unset,
        parameter19_value: Union[str, object] = values.unset,
        parameter20_name: Union[str, object] = values.unset,
        parameter20_value: Union[str, object] = values.unset,
        parameter21_name: Union[str, object] = values.unset,
        parameter21_value: Union[str, object] = values.unset,
        parameter22_name: Union[str, object] = values.unset,
        parameter22_value: Union[str, object] = values.unset,
        parameter23_name: Union[str, object] = values.unset,
        parameter23_value: Union[str, object] = values.unset,
        parameter24_name: Union[str, object] = values.unset,
        parameter24_value: Union[str, object] = values.unset,
        parameter25_name: Union[str, object] = values.unset,
        parameter25_value: Union[str, object] = values.unset,
        parameter26_name: Union[str, object] = values.unset,
        parameter26_value: Union[str, object] = values.unset,
        parameter27_name: Union[str, object] = values.unset,
        parameter27_value: Union[str, object] = values.unset,
        parameter28_name: Union[str, object] = values.unset,
        parameter28_value: Union[str, object] = values.unset,
        parameter29_name: Union[str, object] = values.unset,
        parameter29_value: Union[str, object] = values.unset,
        parameter30_name: Union[str, object] = values.unset,
        parameter30_value: Union[str, object] = values.unset,
        parameter31_name: Union[str, object] = values.unset,
        parameter31_value: Union[str, object] = values.unset,
        parameter32_name: Union[str, object] = values.unset,
        parameter32_value: Union[str, object] = values.unset,
        parameter33_name: Union[str, object] = values.unset,
        parameter33_value: Union[str, object] = values.unset,
        parameter34_name: Union[str, object] = values.unset,
        parameter34_value: Union[str, object] = values.unset,
        parameter35_name: Union[str, object] = values.unset,
        parameter35_value: Union[str, object] = values.unset,
        parameter36_name: Union[str, object] = values.unset,
        parameter36_value: Union[str, object] = values.unset,
        parameter37_name: Union[str, object] = values.unset,
        parameter37_value: Union[str, object] = values.unset,
        parameter38_name: Union[str, object] = values.unset,
        parameter38_value: Union[str, object] = values.unset,
        parameter39_name: Union[str, object] = values.unset,
        parameter39_value: Union[str, object] = values.unset,
        parameter40_name: Union[str, object] = values.unset,
        parameter40_value: Union[str, object] = values.unset,
        parameter41_name: Union[str, object] = values.unset,
        parameter41_value: Union[str, object] = values.unset,
        parameter42_name: Union[str, object] = values.unset,
        parameter42_value: Union[str, object] = values.unset,
        parameter43_name: Union[str, object] = values.unset,
        parameter43_value: Union[str, object] = values.unset,
        parameter44_name: Union[str, object] = values.unset,
        parameter44_value: Union[str, object] = values.unset,
        parameter45_name: Union[str, object] = values.unset,
        parameter45_value: Union[str, object] = values.unset,
        parameter46_name: Union[str, object] = values.unset,
        parameter46_value: Union[str, object] = values.unset,
        parameter47_name: Union[str, object] = values.unset,
        parameter47_value: Union[str, object] = values.unset,
        parameter48_name: Union[str, object] = values.unset,
        parameter48_value: Union[str, object] = values.unset,
        parameter49_name: Union[str, object] = values.unset,
        parameter49_value: Union[str, object] = values.unset,
        parameter50_name: Union[str, object] = values.unset,
        parameter50_value: Union[str, object] = values.unset,
        parameter51_name: Union[str, object] = values.unset,
        parameter51_value: Union[str, object] = values.unset,
        parameter52_name: Union[str, object] = values.unset,
        parameter52_value: Union[str, object] = values.unset,
        parameter53_name: Union[str, object] = values.unset,
        parameter53_value: Union[str, object] = values.unset,
        parameter54_name: Union[str, object] = values.unset,
        parameter54_value: Union[str, object] = values.unset,
        parameter55_name: Union[str, object] = values.unset,
        parameter55_value: Union[str, object] = values.unset,
        parameter56_name: Union[str, object] = values.unset,
        parameter56_value: Union[str, object] = values.unset,
        parameter57_name: Union[str, object] = values.unset,
        parameter57_value: Union[str, object] = values.unset,
        parameter58_name: Union[str, object] = values.unset,
        parameter58_value: Union[str, object] = values.unset,
        parameter59_name: Union[str, object] = values.unset,
        parameter59_value: Union[str, object] = values.unset,
        parameter60_name: Union[str, object] = values.unset,
        parameter60_value: Union[str, object] = values.unset,
        parameter61_name: Union[str, object] = values.unset,
        parameter61_value: Union[str, object] = values.unset,
        parameter62_name: Union[str, object] = values.unset,
        parameter62_value: Union[str, object] = values.unset,
        parameter63_name: Union[str, object] = values.unset,
        parameter63_value: Union[str, object] = values.unset,
        parameter64_name: Union[str, object] = values.unset,
        parameter64_value: Union[str, object] = values.unset,
        parameter65_name: Union[str, object] = values.unset,
        parameter65_value: Union[str, object] = values.unset,
        parameter66_name: Union[str, object] = values.unset,
        parameter66_value: Union[str, object] = values.unset,
        parameter67_name: Union[str, object] = values.unset,
        parameter67_value: Union[str, object] = values.unset,
        parameter68_name: Union[str, object] = values.unset,
        parameter68_value: Union[str, object] = values.unset,
        parameter69_name: Union[str, object] = values.unset,
        parameter69_value: Union[str, object] = values.unset,
        parameter70_name: Union[str, object] = values.unset,
        parameter70_value: Union[str, object] = values.unset,
        parameter71_name: Union[str, object] = values.unset,
        parameter71_value: Union[str, object] = values.unset,
        parameter72_name: Union[str, object] = values.unset,
        parameter72_value: Union[str, object] = values.unset,
        parameter73_name: Union[str, object] = values.unset,
        parameter73_value: Union[str, object] = values.unset,
        parameter74_name: Union[str, object] = values.unset,
        parameter74_value: Union[str, object] = values.unset,
        parameter75_name: Union[str, object] = values.unset,
        parameter75_value: Union[str, object] = values.unset,
        parameter76_name: Union[str, object] = values.unset,
        parameter76_value: Union[str, object] = values.unset,
        parameter77_name: Union[str, object] = values.unset,
        parameter77_value: Union[str, object] = values.unset,
        parameter78_name: Union[str, object] = values.unset,
        parameter78_value: Union[str, object] = values.unset,
        parameter79_name: Union[str, object] = values.unset,
        parameter79_value: Union[str, object] = values.unset,
        parameter80_name: Union[str, object] = values.unset,
        parameter80_value: Union[str, object] = values.unset,
        parameter81_name: Union[str, object] = values.unset,
        parameter81_value: Union[str, object] = values.unset,
        parameter82_name: Union[str, object] = values.unset,
        parameter82_value: Union[str, object] = values.unset,
        parameter83_name: Union[str, object] = values.unset,
        parameter83_value: Union[str, object] = values.unset,
        parameter84_name: Union[str, object] = values.unset,
        parameter84_value: Union[str, object] = values.unset,
        parameter85_name: Union[str, object] = values.unset,
        parameter85_value: Union[str, object] = values.unset,
        parameter86_name: Union[str, object] = values.unset,
        parameter86_value: Union[str, object] = values.unset,
        parameter87_name: Union[str, object] = values.unset,
        parameter87_value: Union[str, object] = values.unset,
        parameter88_name: Union[str, object] = values.unset,
        parameter88_value: Union[str, object] = values.unset,
        parameter89_name: Union[str, object] = values.unset,
        parameter89_value: Union[str, object] = values.unset,
        parameter90_name: Union[str, object] = values.unset,
        parameter90_value: Union[str, object] = values.unset,
        parameter91_name: Union[str, object] = values.unset,
        parameter91_value: Union[str, object] = values.unset,
        parameter92_name: Union[str, object] = values.unset,
        parameter92_value: Union[str, object] = values.unset,
        parameter93_name: Union[str, object] = values.unset,
        parameter93_value: Union[str, object] = values.unset,
        parameter94_name: Union[str, object] = values.unset,
        parameter94_value: Union[str, object] = values.unset,
        parameter95_name: Union[str, object] = values.unset,
        parameter95_value: Union[str, object] = values.unset,
        parameter96_name: Union[str, object] = values.unset,
        parameter96_value: Union[str, object] = values.unset,
        parameter97_name: Union[str, object] = values.unset,
        parameter97_value: Union[str, object] = values.unset,
        parameter98_name: Union[str, object] = values.unset,
        parameter98_value: Union[str, object] = values.unset,
        parameter99_name: Union[str, object] = values.unset,
        parameter99_value: Union[str, object] = values.unset,
    ) -> StreamInstance:
        """
        Create the StreamInstance

        :param url: Relative or absolute URL where WebSocket connection will be established.
        :param name: The user-specified name of this Stream, if one was given when the Stream was created. This can be used to stop the Stream.
        :param track:
        :param status_callback: Absolute URL to which Twilio sends status callback HTTP requests.
        :param status_callback_method: The HTTP method Twilio uses when sending `status_callback` requests. Possible values are `GET` and `POST`. Default is `POST`.
        :param parameter1_name: Parameter name
        :param parameter1_value: Parameter value
        :param parameter2_name: Parameter name
        :param parameter2_value: Parameter value
        :param parameter3_name: Parameter name
        :param parameter3_value: Parameter value
        :param parameter4_name: Parameter name
        :param parameter4_value: Parameter value
        :param parameter5_name: Parameter name
        :param parameter5_value: Parameter value
        :param parameter6_name: Parameter name
        :param parameter6_value: Parameter value
        :param parameter7_name: Parameter name
        :param parameter7_value: Parameter value
        :param parameter8_name: Parameter name
        :param parameter8_value: Parameter value
        :param parameter9_name: Parameter name
        :param parameter9_value: Parameter value
        :param parameter10_name: Parameter name
        :param parameter10_value: Parameter value
        :param parameter11_name: Parameter name
        :param parameter11_value: Parameter value
        :param parameter12_name: Parameter name
        :param parameter12_value: Parameter value
        :param parameter13_name: Parameter name
        :param parameter13_value: Parameter value
        :param parameter14_name: Parameter name
        :param parameter14_value: Parameter value
        :param parameter15_name: Parameter name
        :param parameter15_value: Parameter value
        :param parameter16_name: Parameter name
        :param parameter16_value: Parameter value
        :param parameter17_name: Parameter name
        :param parameter17_value: Parameter value
        :param parameter18_name: Parameter name
        :param parameter18_value: Parameter value
        :param parameter19_name: Parameter name
        :param parameter19_value: Parameter value
        :param parameter20_name: Parameter name
        :param parameter20_value: Parameter value
        :param parameter21_name: Parameter name
        :param parameter21_value: Parameter value
        :param parameter22_name: Parameter name
        :param parameter22_value: Parameter value
        :param parameter23_name: Parameter name
        :param parameter23_value: Parameter value
        :param parameter24_name: Parameter name
        :param parameter24_value: Parameter value
        :param parameter25_name: Parameter name
        :param parameter25_value: Parameter value
        :param parameter26_name: Parameter name
        :param parameter26_value: Parameter value
        :param parameter27_name: Parameter name
        :param parameter27_value: Parameter value
        :param parameter28_name: Parameter name
        :param parameter28_value: Parameter value
        :param parameter29_name: Parameter name
        :param parameter29_value: Parameter value
        :param parameter30_name: Parameter name
        :param parameter30_value: Parameter value
        :param parameter31_name: Parameter name
        :param parameter31_value: Parameter value
        :param parameter32_name: Parameter name
        :param parameter32_value: Parameter value
        :param parameter33_name: Parameter name
        :param parameter33_value: Parameter value
        :param parameter34_name: Parameter name
        :param parameter34_value: Parameter value
        :param parameter35_name: Parameter name
        :param parameter35_value: Parameter value
        :param parameter36_name: Parameter name
        :param parameter36_value: Parameter value
        :param parameter37_name: Parameter name
        :param parameter37_value: Parameter value
        :param parameter38_name: Parameter name
        :param parameter38_value: Parameter value
        :param parameter39_name: Parameter name
        :param parameter39_value: Parameter value
        :param parameter40_name: Parameter name
        :param parameter40_value: Parameter value
        :param parameter41_name: Parameter name
        :param parameter41_value: Parameter value
        :param parameter42_name: Parameter name
        :param parameter42_value: Parameter value
        :param parameter43_name: Parameter name
        :param parameter43_value: Parameter value
        :param parameter44_name: Parameter name
        :param parameter44_value: Parameter value
        :param parameter45_name: Parameter name
        :param parameter45_value: Parameter value
        :param parameter46_name: Parameter name
        :param parameter46_value: Parameter value
        :param parameter47_name: Parameter name
        :param parameter47_value: Parameter value
        :param parameter48_name: Parameter name
        :param parameter48_value: Parameter value
        :param parameter49_name: Parameter name
        :param parameter49_value: Parameter value
        :param parameter50_name: Parameter name
        :param parameter50_value: Parameter value
        :param parameter51_name: Parameter name
        :param parameter51_value: Parameter value
        :param parameter52_name: Parameter name
        :param parameter52_value: Parameter value
        :param parameter53_name: Parameter name
        :param parameter53_value: Parameter value
        :param parameter54_name: Parameter name
        :param parameter54_value: Parameter value
        :param parameter55_name: Parameter name
        :param parameter55_value: Parameter value
        :param parameter56_name: Parameter name
        :param parameter56_value: Parameter value
        :param parameter57_name: Parameter name
        :param parameter57_value: Parameter value
        :param parameter58_name: Parameter name
        :param parameter58_value: Parameter value
        :param parameter59_name: Parameter name
        :param parameter59_value: Parameter value
        :param parameter60_name: Parameter name
        :param parameter60_value: Parameter value
        :param parameter61_name: Parameter name
        :param parameter61_value: Parameter value
        :param parameter62_name: Parameter name
        :param parameter62_value: Parameter value
        :param parameter63_name: Parameter name
        :param parameter63_value: Parameter value
        :param parameter64_name: Parameter name
        :param parameter64_value: Parameter value
        :param parameter65_name: Parameter name
        :param parameter65_value: Parameter value
        :param parameter66_name: Parameter name
        :param parameter66_value: Parameter value
        :param parameter67_name: Parameter name
        :param parameter67_value: Parameter value
        :param parameter68_name: Parameter name
        :param parameter68_value: Parameter value
        :param parameter69_name: Parameter name
        :param parameter69_value: Parameter value
        :param parameter70_name: Parameter name
        :param parameter70_value: Parameter value
        :param parameter71_name: Parameter name
        :param parameter71_value: Parameter value
        :param parameter72_name: Parameter name
        :param parameter72_value: Parameter value
        :param parameter73_name: Parameter name
        :param parameter73_value: Parameter value
        :param parameter74_name: Parameter name
        :param parameter74_value: Parameter value
        :param parameter75_name: Parameter name
        :param parameter75_value: Parameter value
        :param parameter76_name: Parameter name
        :param parameter76_value: Parameter value
        :param parameter77_name: Parameter name
        :param parameter77_value: Parameter value
        :param parameter78_name: Parameter name
        :param parameter78_value: Parameter value
        :param parameter79_name: Parameter name
        :param parameter79_value: Parameter value
        :param parameter80_name: Parameter name
        :param parameter80_value: Parameter value
        :param parameter81_name: Parameter name
        :param parameter81_value: Parameter value
        :param parameter82_name: Parameter name
        :param parameter82_value: Parameter value
        :param parameter83_name: Parameter name
        :param parameter83_value: Parameter value
        :param parameter84_name: Parameter name
        :param parameter84_value: Parameter value
        :param parameter85_name: Parameter name
        :param parameter85_value: Parameter value
        :param parameter86_name: Parameter name
        :param parameter86_value: Parameter value
        :param parameter87_name: Parameter name
        :param parameter87_value: Parameter value
        :param parameter88_name: Parameter name
        :param parameter88_value: Parameter value
        :param parameter89_name: Parameter name
        :param parameter89_value: Parameter value
        :param parameter90_name: Parameter name
        :param parameter90_value: Parameter value
        :param parameter91_name: Parameter name
        :param parameter91_value: Parameter value
        :param parameter92_name: Parameter name
        :param parameter92_value: Parameter value
        :param parameter93_name: Parameter name
        :param parameter93_value: Parameter value
        :param parameter94_name: Parameter name
        :param parameter94_value: Parameter value
        :param parameter95_name: Parameter name
        :param parameter95_value: Parameter value
        :param parameter96_name: Parameter name
        :param parameter96_value: Parameter value
        :param parameter97_name: Parameter name
        :param parameter97_value: Parameter value
        :param parameter98_name: Parameter name
        :param parameter98_value: Parameter value
        :param parameter99_name: Parameter name
        :param parameter99_value: Parameter value

        :returns: The created StreamInstance
        """

        data = values.of(
            {
                "Url": url,
                "Name": name,
                "Track": track,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "Parameter1.Name": parameter1_name,
                "Parameter1.Value": parameter1_value,
                "Parameter2.Name": parameter2_name,
                "Parameter2.Value": parameter2_value,
                "Parameter3.Name": parameter3_name,
                "Parameter3.Value": parameter3_value,
                "Parameter4.Name": parameter4_name,
                "Parameter4.Value": parameter4_value,
                "Parameter5.Name": parameter5_name,
                "Parameter5.Value": parameter5_value,
                "Parameter6.Name": parameter6_name,
                "Parameter6.Value": parameter6_value,
                "Parameter7.Name": parameter7_name,
                "Parameter7.Value": parameter7_value,
                "Parameter8.Name": parameter8_name,
                "Parameter8.Value": parameter8_value,
                "Parameter9.Name": parameter9_name,
                "Parameter9.Value": parameter9_value,
                "Parameter10.Name": parameter10_name,
                "Parameter10.Value": parameter10_value,
                "Parameter11.Name": parameter11_name,
                "Parameter11.Value": parameter11_value,
                "Parameter12.Name": parameter12_name,
                "Parameter12.Value": parameter12_value,
                "Parameter13.Name": parameter13_name,
                "Parameter13.Value": parameter13_value,
                "Parameter14.Name": parameter14_name,
                "Parameter14.Value": parameter14_value,
                "Parameter15.Name": parameter15_name,
                "Parameter15.Value": parameter15_value,
                "Parameter16.Name": parameter16_name,
                "Parameter16.Value": parameter16_value,
                "Parameter17.Name": parameter17_name,
                "Parameter17.Value": parameter17_value,
                "Parameter18.Name": parameter18_name,
                "Parameter18.Value": parameter18_value,
                "Parameter19.Name": parameter19_name,
                "Parameter19.Value": parameter19_value,
                "Parameter20.Name": parameter20_name,
                "Parameter20.Value": parameter20_value,
                "Parameter21.Name": parameter21_name,
                "Parameter21.Value": parameter21_value,
                "Parameter22.Name": parameter22_name,
                "Parameter22.Value": parameter22_value,
                "Parameter23.Name": parameter23_name,
                "Parameter23.Value": parameter23_value,
                "Parameter24.Name": parameter24_name,
                "Parameter24.Value": parameter24_value,
                "Parameter25.Name": parameter25_name,
                "Parameter25.Value": parameter25_value,
                "Parameter26.Name": parameter26_name,
                "Parameter26.Value": parameter26_value,
                "Parameter27.Name": parameter27_name,
                "Parameter27.Value": parameter27_value,
                "Parameter28.Name": parameter28_name,
                "Parameter28.Value": parameter28_value,
                "Parameter29.Name": parameter29_name,
                "Parameter29.Value": parameter29_value,
                "Parameter30.Name": parameter30_name,
                "Parameter30.Value": parameter30_value,
                "Parameter31.Name": parameter31_name,
                "Parameter31.Value": parameter31_value,
                "Parameter32.Name": parameter32_name,
                "Parameter32.Value": parameter32_value,
                "Parameter33.Name": parameter33_name,
                "Parameter33.Value": parameter33_value,
                "Parameter34.Name": parameter34_name,
                "Parameter34.Value": parameter34_value,
                "Parameter35.Name": parameter35_name,
                "Parameter35.Value": parameter35_value,
                "Parameter36.Name": parameter36_name,
                "Parameter36.Value": parameter36_value,
                "Parameter37.Name": parameter37_name,
                "Parameter37.Value": parameter37_value,
                "Parameter38.Name": parameter38_name,
                "Parameter38.Value": parameter38_value,
                "Parameter39.Name": parameter39_name,
                "Parameter39.Value": parameter39_value,
                "Parameter40.Name": parameter40_name,
                "Parameter40.Value": parameter40_value,
                "Parameter41.Name": parameter41_name,
                "Parameter41.Value": parameter41_value,
                "Parameter42.Name": parameter42_name,
                "Parameter42.Value": parameter42_value,
                "Parameter43.Name": parameter43_name,
                "Parameter43.Value": parameter43_value,
                "Parameter44.Name": parameter44_name,
                "Parameter44.Value": parameter44_value,
                "Parameter45.Name": parameter45_name,
                "Parameter45.Value": parameter45_value,
                "Parameter46.Name": parameter46_name,
                "Parameter46.Value": parameter46_value,
                "Parameter47.Name": parameter47_name,
                "Parameter47.Value": parameter47_value,
                "Parameter48.Name": parameter48_name,
                "Parameter48.Value": parameter48_value,
                "Parameter49.Name": parameter49_name,
                "Parameter49.Value": parameter49_value,
                "Parameter50.Name": parameter50_name,
                "Parameter50.Value": parameter50_value,
                "Parameter51.Name": parameter51_name,
                "Parameter51.Value": parameter51_value,
                "Parameter52.Name": parameter52_name,
                "Parameter52.Value": parameter52_value,
                "Parameter53.Name": parameter53_name,
                "Parameter53.Value": parameter53_value,
                "Parameter54.Name": parameter54_name,
                "Parameter54.Value": parameter54_value,
                "Parameter55.Name": parameter55_name,
                "Parameter55.Value": parameter55_value,
                "Parameter56.Name": parameter56_name,
                "Parameter56.Value": parameter56_value,
                "Parameter57.Name": parameter57_name,
                "Parameter57.Value": parameter57_value,
                "Parameter58.Name": parameter58_name,
                "Parameter58.Value": parameter58_value,
                "Parameter59.Name": parameter59_name,
                "Parameter59.Value": parameter59_value,
                "Parameter60.Name": parameter60_name,
                "Parameter60.Value": parameter60_value,
                "Parameter61.Name": parameter61_name,
                "Parameter61.Value": parameter61_value,
                "Parameter62.Name": parameter62_name,
                "Parameter62.Value": parameter62_value,
                "Parameter63.Name": parameter63_name,
                "Parameter63.Value": parameter63_value,
                "Parameter64.Name": parameter64_name,
                "Parameter64.Value": parameter64_value,
                "Parameter65.Name": parameter65_name,
                "Parameter65.Value": parameter65_value,
                "Parameter66.Name": parameter66_name,
                "Parameter66.Value": parameter66_value,
                "Parameter67.Name": parameter67_name,
                "Parameter67.Value": parameter67_value,
                "Parameter68.Name": parameter68_name,
                "Parameter68.Value": parameter68_value,
                "Parameter69.Name": parameter69_name,
                "Parameter69.Value": parameter69_value,
                "Parameter70.Name": parameter70_name,
                "Parameter70.Value": parameter70_value,
                "Parameter71.Name": parameter71_name,
                "Parameter71.Value": parameter71_value,
                "Parameter72.Name": parameter72_name,
                "Parameter72.Value": parameter72_value,
                "Parameter73.Name": parameter73_name,
                "Parameter73.Value": parameter73_value,
                "Parameter74.Name": parameter74_name,
                "Parameter74.Value": parameter74_value,
                "Parameter75.Name": parameter75_name,
                "Parameter75.Value": parameter75_value,
                "Parameter76.Name": parameter76_name,
                "Parameter76.Value": parameter76_value,
                "Parameter77.Name": parameter77_name,
                "Parameter77.Value": parameter77_value,
                "Parameter78.Name": parameter78_name,
                "Parameter78.Value": parameter78_value,
                "Parameter79.Name": parameter79_name,
                "Parameter79.Value": parameter79_value,
                "Parameter80.Name": parameter80_name,
                "Parameter80.Value": parameter80_value,
                "Parameter81.Name": parameter81_name,
                "Parameter81.Value": parameter81_value,
                "Parameter82.Name": parameter82_name,
                "Parameter82.Value": parameter82_value,
                "Parameter83.Name": parameter83_name,
                "Parameter83.Value": parameter83_value,
                "Parameter84.Name": parameter84_name,
                "Parameter84.Value": parameter84_value,
                "Parameter85.Name": parameter85_name,
                "Parameter85.Value": parameter85_value,
                "Parameter86.Name": parameter86_name,
                "Parameter86.Value": parameter86_value,
                "Parameter87.Name": parameter87_name,
                "Parameter87.Value": parameter87_value,
                "Parameter88.Name": parameter88_name,
                "Parameter88.Value": parameter88_value,
                "Parameter89.Name": parameter89_name,
                "Parameter89.Value": parameter89_value,
                "Parameter90.Name": parameter90_name,
                "Parameter90.Value": parameter90_value,
                "Parameter91.Name": parameter91_name,
                "Parameter91.Value": parameter91_value,
                "Parameter92.Name": parameter92_name,
                "Parameter92.Value": parameter92_value,
                "Parameter93.Name": parameter93_name,
                "Parameter93.Value": parameter93_value,
                "Parameter94.Name": parameter94_name,
                "Parameter94.Value": parameter94_value,
                "Parameter95.Name": parameter95_name,
                "Parameter95.Value": parameter95_value,
                "Parameter96.Name": parameter96_name,
                "Parameter96.Value": parameter96_value,
                "Parameter97.Name": parameter97_name,
                "Parameter97.Value": parameter97_value,
                "Parameter98.Name": parameter98_name,
                "Parameter98.Value": parameter98_value,
                "Parameter99.Name": parameter99_name,
                "Parameter99.Value": parameter99_value,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return StreamInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            call_sid=self._solution["call_sid"],
        )

    async def create_async(
        self,
        url: str,
        name: Union[str, object] = values.unset,
        track: Union["StreamInstance.Track", object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        parameter1_name: Union[str, object] = values.unset,
        parameter1_value: Union[str, object] = values.unset,
        parameter2_name: Union[str, object] = values.unset,
        parameter2_value: Union[str, object] = values.unset,
        parameter3_name: Union[str, object] = values.unset,
        parameter3_value: Union[str, object] = values.unset,
        parameter4_name: Union[str, object] = values.unset,
        parameter4_value: Union[str, object] = values.unset,
        parameter5_name: Union[str, object] = values.unset,
        parameter5_value: Union[str, object] = values.unset,
        parameter6_name: Union[str, object] = values.unset,
        parameter6_value: Union[str, object] = values.unset,
        parameter7_name: Union[str, object] = values.unset,
        parameter7_value: Union[str, object] = values.unset,
        parameter8_name: Union[str, object] = values.unset,
        parameter8_value: Union[str, object] = values.unset,
        parameter9_name: Union[str, object] = values.unset,
        parameter9_value: Union[str, object] = values.unset,
        parameter10_name: Union[str, object] = values.unset,
        parameter10_value: Union[str, object] = values.unset,
        parameter11_name: Union[str, object] = values.unset,
        parameter11_value: Union[str, object] = values.unset,
        parameter12_name: Union[str, object] = values.unset,
        parameter12_value: Union[str, object] = values.unset,
        parameter13_name: Union[str, object] = values.unset,
        parameter13_value: Union[str, object] = values.unset,
        parameter14_name: Union[str, object] = values.unset,
        parameter14_value: Union[str, object] = values.unset,
        parameter15_name: Union[str, object] = values.unset,
        parameter15_value: Union[str, object] = values.unset,
        parameter16_name: Union[str, object] = values.unset,
        parameter16_value: Union[str, object] = values.unset,
        parameter17_name: Union[str, object] = values.unset,
        parameter17_value: Union[str, object] = values.unset,
        parameter18_name: Union[str, object] = values.unset,
        parameter18_value: Union[str, object] = values.unset,
        parameter19_name: Union[str, object] = values.unset,
        parameter19_value: Union[str, object] = values.unset,
        parameter20_name: Union[str, object] = values.unset,
        parameter20_value: Union[str, object] = values.unset,
        parameter21_name: Union[str, object] = values.unset,
        parameter21_value: Union[str, object] = values.unset,
        parameter22_name: Union[str, object] = values.unset,
        parameter22_value: Union[str, object] = values.unset,
        parameter23_name: Union[str, object] = values.unset,
        parameter23_value: Union[str, object] = values.unset,
        parameter24_name: Union[str, object] = values.unset,
        parameter24_value: Union[str, object] = values.unset,
        parameter25_name: Union[str, object] = values.unset,
        parameter25_value: Union[str, object] = values.unset,
        parameter26_name: Union[str, object] = values.unset,
        parameter26_value: Union[str, object] = values.unset,
        parameter27_name: Union[str, object] = values.unset,
        parameter27_value: Union[str, object] = values.unset,
        parameter28_name: Union[str, object] = values.unset,
        parameter28_value: Union[str, object] = values.unset,
        parameter29_name: Union[str, object] = values.unset,
        parameter29_value: Union[str, object] = values.unset,
        parameter30_name: Union[str, object] = values.unset,
        parameter30_value: Union[str, object] = values.unset,
        parameter31_name: Union[str, object] = values.unset,
        parameter31_value: Union[str, object] = values.unset,
        parameter32_name: Union[str, object] = values.unset,
        parameter32_value: Union[str, object] = values.unset,
        parameter33_name: Union[str, object] = values.unset,
        parameter33_value: Union[str, object] = values.unset,
        parameter34_name: Union[str, object] = values.unset,
        parameter34_value: Union[str, object] = values.unset,
        parameter35_name: Union[str, object] = values.unset,
        parameter35_value: Union[str, object] = values.unset,
        parameter36_name: Union[str, object] = values.unset,
        parameter36_value: Union[str, object] = values.unset,
        parameter37_name: Union[str, object] = values.unset,
        parameter37_value: Union[str, object] = values.unset,
        parameter38_name: Union[str, object] = values.unset,
        parameter38_value: Union[str, object] = values.unset,
        parameter39_name: Union[str, object] = values.unset,
        parameter39_value: Union[str, object] = values.unset,
        parameter40_name: Union[str, object] = values.unset,
        parameter40_value: Union[str, object] = values.unset,
        parameter41_name: Union[str, object] = values.unset,
        parameter41_value: Union[str, object] = values.unset,
        parameter42_name: Union[str, object] = values.unset,
        parameter42_value: Union[str, object] = values.unset,
        parameter43_name: Union[str, object] = values.unset,
        parameter43_value: Union[str, object] = values.unset,
        parameter44_name: Union[str, object] = values.unset,
        parameter44_value: Union[str, object] = values.unset,
        parameter45_name: Union[str, object] = values.unset,
        parameter45_value: Union[str, object] = values.unset,
        parameter46_name: Union[str, object] = values.unset,
        parameter46_value: Union[str, object] = values.unset,
        parameter47_name: Union[str, object] = values.unset,
        parameter47_value: Union[str, object] = values.unset,
        parameter48_name: Union[str, object] = values.unset,
        parameter48_value: Union[str, object] = values.unset,
        parameter49_name: Union[str, object] = values.unset,
        parameter49_value: Union[str, object] = values.unset,
        parameter50_name: Union[str, object] = values.unset,
        parameter50_value: Union[str, object] = values.unset,
        parameter51_name: Union[str, object] = values.unset,
        parameter51_value: Union[str, object] = values.unset,
        parameter52_name: Union[str, object] = values.unset,
        parameter52_value: Union[str, object] = values.unset,
        parameter53_name: Union[str, object] = values.unset,
        parameter53_value: Union[str, object] = values.unset,
        parameter54_name: Union[str, object] = values.unset,
        parameter54_value: Union[str, object] = values.unset,
        parameter55_name: Union[str, object] = values.unset,
        parameter55_value: Union[str, object] = values.unset,
        parameter56_name: Union[str, object] = values.unset,
        parameter56_value: Union[str, object] = values.unset,
        parameter57_name: Union[str, object] = values.unset,
        parameter57_value: Union[str, object] = values.unset,
        parameter58_name: Union[str, object] = values.unset,
        parameter58_value: Union[str, object] = values.unset,
        parameter59_name: Union[str, object] = values.unset,
        parameter59_value: Union[str, object] = values.unset,
        parameter60_name: Union[str, object] = values.unset,
        parameter60_value: Union[str, object] = values.unset,
        parameter61_name: Union[str, object] = values.unset,
        parameter61_value: Union[str, object] = values.unset,
        parameter62_name: Union[str, object] = values.unset,
        parameter62_value: Union[str, object] = values.unset,
        parameter63_name: Union[str, object] = values.unset,
        parameter63_value: Union[str, object] = values.unset,
        parameter64_name: Union[str, object] = values.unset,
        parameter64_value: Union[str, object] = values.unset,
        parameter65_name: Union[str, object] = values.unset,
        parameter65_value: Union[str, object] = values.unset,
        parameter66_name: Union[str, object] = values.unset,
        parameter66_value: Union[str, object] = values.unset,
        parameter67_name: Union[str, object] = values.unset,
        parameter67_value: Union[str, object] = values.unset,
        parameter68_name: Union[str, object] = values.unset,
        parameter68_value: Union[str, object] = values.unset,
        parameter69_name: Union[str, object] = values.unset,
        parameter69_value: Union[str, object] = values.unset,
        parameter70_name: Union[str, object] = values.unset,
        parameter70_value: Union[str, object] = values.unset,
        parameter71_name: Union[str, object] = values.unset,
        parameter71_value: Union[str, object] = values.unset,
        parameter72_name: Union[str, object] = values.unset,
        parameter72_value: Union[str, object] = values.unset,
        parameter73_name: Union[str, object] = values.unset,
        parameter73_value: Union[str, object] = values.unset,
        parameter74_name: Union[str, object] = values.unset,
        parameter74_value: Union[str, object] = values.unset,
        parameter75_name: Union[str, object] = values.unset,
        parameter75_value: Union[str, object] = values.unset,
        parameter76_name: Union[str, object] = values.unset,
        parameter76_value: Union[str, object] = values.unset,
        parameter77_name: Union[str, object] = values.unset,
        parameter77_value: Union[str, object] = values.unset,
        parameter78_name: Union[str, object] = values.unset,
        parameter78_value: Union[str, object] = values.unset,
        parameter79_name: Union[str, object] = values.unset,
        parameter79_value: Union[str, object] = values.unset,
        parameter80_name: Union[str, object] = values.unset,
        parameter80_value: Union[str, object] = values.unset,
        parameter81_name: Union[str, object] = values.unset,
        parameter81_value: Union[str, object] = values.unset,
        parameter82_name: Union[str, object] = values.unset,
        parameter82_value: Union[str, object] = values.unset,
        parameter83_name: Union[str, object] = values.unset,
        parameter83_value: Union[str, object] = values.unset,
        parameter84_name: Union[str, object] = values.unset,
        parameter84_value: Union[str, object] = values.unset,
        parameter85_name: Union[str, object] = values.unset,
        parameter85_value: Union[str, object] = values.unset,
        parameter86_name: Union[str, object] = values.unset,
        parameter86_value: Union[str, object] = values.unset,
        parameter87_name: Union[str, object] = values.unset,
        parameter87_value: Union[str, object] = values.unset,
        parameter88_name: Union[str, object] = values.unset,
        parameter88_value: Union[str, object] = values.unset,
        parameter89_name: Union[str, object] = values.unset,
        parameter89_value: Union[str, object] = values.unset,
        parameter90_name: Union[str, object] = values.unset,
        parameter90_value: Union[str, object] = values.unset,
        parameter91_name: Union[str, object] = values.unset,
        parameter91_value: Union[str, object] = values.unset,
        parameter92_name: Union[str, object] = values.unset,
        parameter92_value: Union[str, object] = values.unset,
        parameter93_name: Union[str, object] = values.unset,
        parameter93_value: Union[str, object] = values.unset,
        parameter94_name: Union[str, object] = values.unset,
        parameter94_value: Union[str, object] = values.unset,
        parameter95_name: Union[str, object] = values.unset,
        parameter95_value: Union[str, object] = values.unset,
        parameter96_name: Union[str, object] = values.unset,
        parameter96_value: Union[str, object] = values.unset,
        parameter97_name: Union[str, object] = values.unset,
        parameter97_value: Union[str, object] = values.unset,
        parameter98_name: Union[str, object] = values.unset,
        parameter98_value: Union[str, object] = values.unset,
        parameter99_name: Union[str, object] = values.unset,
        parameter99_value: Union[str, object] = values.unset,
    ) -> StreamInstance:
        """
        Asynchronously create the StreamInstance

        :param url: Relative or absolute URL where WebSocket connection will be established.
        :param name: The user-specified name of this Stream, if one was given when the Stream was created. This can be used to stop the Stream.
        :param track:
        :param status_callback: Absolute URL to which Twilio sends status callback HTTP requests.
        :param status_callback_method: The HTTP method Twilio uses when sending `status_callback` requests. Possible values are `GET` and `POST`. Default is `POST`.
        :param parameter1_name: Parameter name
        :param parameter1_value: Parameter value
        :param parameter2_name: Parameter name
        :param parameter2_value: Parameter value
        :param parameter3_name: Parameter name
        :param parameter3_value: Parameter value
        :param parameter4_name: Parameter name
        :param parameter4_value: Parameter value
        :param parameter5_name: Parameter name
        :param parameter5_value: Parameter value
        :param parameter6_name: Parameter name
        :param parameter6_value: Parameter value
        :param parameter7_name: Parameter name
        :param parameter7_value: Parameter value
        :param parameter8_name: Parameter name
        :param parameter8_value: Parameter value
        :param parameter9_name: Parameter name
        :param parameter9_value: Parameter value
        :param parameter10_name: Parameter name
        :param parameter10_value: Parameter value
        :param parameter11_name: Parameter name
        :param parameter11_value: Parameter value
        :param parameter12_name: Parameter name
        :param parameter12_value: Parameter value
        :param parameter13_name: Parameter name
        :param parameter13_value: Parameter value
        :param parameter14_name: Parameter name
        :param parameter14_value: Parameter value
        :param parameter15_name: Parameter name
        :param parameter15_value: Parameter value
        :param parameter16_name: Parameter name
        :param parameter16_value: Parameter value
        :param parameter17_name: Parameter name
        :param parameter17_value: Parameter value
        :param parameter18_name: Parameter name
        :param parameter18_value: Parameter value
        :param parameter19_name: Parameter name
        :param parameter19_value: Parameter value
        :param parameter20_name: Parameter name
        :param parameter20_value: Parameter value
        :param parameter21_name: Parameter name
        :param parameter21_value: Parameter value
        :param parameter22_name: Parameter name
        :param parameter22_value: Parameter value
        :param parameter23_name: Parameter name
        :param parameter23_value: Parameter value
        :param parameter24_name: Parameter name
        :param parameter24_value: Parameter value
        :param parameter25_name: Parameter name
        :param parameter25_value: Parameter value
        :param parameter26_name: Parameter name
        :param parameter26_value: Parameter value
        :param parameter27_name: Parameter name
        :param parameter27_value: Parameter value
        :param parameter28_name: Parameter name
        :param parameter28_value: Parameter value
        :param parameter29_name: Parameter name
        :param parameter29_value: Parameter value
        :param parameter30_name: Parameter name
        :param parameter30_value: Parameter value
        :param parameter31_name: Parameter name
        :param parameter31_value: Parameter value
        :param parameter32_name: Parameter name
        :param parameter32_value: Parameter value
        :param parameter33_name: Parameter name
        :param parameter33_value: Parameter value
        :param parameter34_name: Parameter name
        :param parameter34_value: Parameter value
        :param parameter35_name: Parameter name
        :param parameter35_value: Parameter value
        :param parameter36_name: Parameter name
        :param parameter36_value: Parameter value
        :param parameter37_name: Parameter name
        :param parameter37_value: Parameter value
        :param parameter38_name: Parameter name
        :param parameter38_value: Parameter value
        :param parameter39_name: Parameter name
        :param parameter39_value: Parameter value
        :param parameter40_name: Parameter name
        :param parameter40_value: Parameter value
        :param parameter41_name: Parameter name
        :param parameter41_value: Parameter value
        :param parameter42_name: Parameter name
        :param parameter42_value: Parameter value
        :param parameter43_name: Parameter name
        :param parameter43_value: Parameter value
        :param parameter44_name: Parameter name
        :param parameter44_value: Parameter value
        :param parameter45_name: Parameter name
        :param parameter45_value: Parameter value
        :param parameter46_name: Parameter name
        :param parameter46_value: Parameter value
        :param parameter47_name: Parameter name
        :param parameter47_value: Parameter value
        :param parameter48_name: Parameter name
        :param parameter48_value: Parameter value
        :param parameter49_name: Parameter name
        :param parameter49_value: Parameter value
        :param parameter50_name: Parameter name
        :param parameter50_value: Parameter value
        :param parameter51_name: Parameter name
        :param parameter51_value: Parameter value
        :param parameter52_name: Parameter name
        :param parameter52_value: Parameter value
        :param parameter53_name: Parameter name
        :param parameter53_value: Parameter value
        :param parameter54_name: Parameter name
        :param parameter54_value: Parameter value
        :param parameter55_name: Parameter name
        :param parameter55_value: Parameter value
        :param parameter56_name: Parameter name
        :param parameter56_value: Parameter value
        :param parameter57_name: Parameter name
        :param parameter57_value: Parameter value
        :param parameter58_name: Parameter name
        :param parameter58_value: Parameter value
        :param parameter59_name: Parameter name
        :param parameter59_value: Parameter value
        :param parameter60_name: Parameter name
        :param parameter60_value: Parameter value
        :param parameter61_name: Parameter name
        :param parameter61_value: Parameter value
        :param parameter62_name: Parameter name
        :param parameter62_value: Parameter value
        :param parameter63_name: Parameter name
        :param parameter63_value: Parameter value
        :param parameter64_name: Parameter name
        :param parameter64_value: Parameter value
        :param parameter65_name: Parameter name
        :param parameter65_value: Parameter value
        :param parameter66_name: Parameter name
        :param parameter66_value: Parameter value
        :param parameter67_name: Parameter name
        :param parameter67_value: Parameter value
        :param parameter68_name: Parameter name
        :param parameter68_value: Parameter value
        :param parameter69_name: Parameter name
        :param parameter69_value: Parameter value
        :param parameter70_name: Parameter name
        :param parameter70_value: Parameter value
        :param parameter71_name: Parameter name
        :param parameter71_value: Parameter value
        :param parameter72_name: Parameter name
        :param parameter72_value: Parameter value
        :param parameter73_name: Parameter name
        :param parameter73_value: Parameter value
        :param parameter74_name: Parameter name
        :param parameter74_value: Parameter value
        :param parameter75_name: Parameter name
        :param parameter75_value: Parameter value
        :param parameter76_name: Parameter name
        :param parameter76_value: Parameter value
        :param parameter77_name: Parameter name
        :param parameter77_value: Parameter value
        :param parameter78_name: Parameter name
        :param parameter78_value: Parameter value
        :param parameter79_name: Parameter name
        :param parameter79_value: Parameter value
        :param parameter80_name: Parameter name
        :param parameter80_value: Parameter value
        :param parameter81_name: Parameter name
        :param parameter81_value: Parameter value
        :param parameter82_name: Parameter name
        :param parameter82_value: Parameter value
        :param parameter83_name: Parameter name
        :param parameter83_value: Parameter value
        :param parameter84_name: Parameter name
        :param parameter84_value: Parameter value
        :param parameter85_name: Parameter name
        :param parameter85_value: Parameter value
        :param parameter86_name: Parameter name
        :param parameter86_value: Parameter value
        :param parameter87_name: Parameter name
        :param parameter87_value: Parameter value
        :param parameter88_name: Parameter name
        :param parameter88_value: Parameter value
        :param parameter89_name: Parameter name
        :param parameter89_value: Parameter value
        :param parameter90_name: Parameter name
        :param parameter90_value: Parameter value
        :param parameter91_name: Parameter name
        :param parameter91_value: Parameter value
        :param parameter92_name: Parameter name
        :param parameter92_value: Parameter value
        :param parameter93_name: Parameter name
        :param parameter93_value: Parameter value
        :param parameter94_name: Parameter name
        :param parameter94_value: Parameter value
        :param parameter95_name: Parameter name
        :param parameter95_value: Parameter value
        :param parameter96_name: Parameter name
        :param parameter96_value: Parameter value
        :param parameter97_name: Parameter name
        :param parameter97_value: Parameter value
        :param parameter98_name: Parameter name
        :param parameter98_value: Parameter value
        :param parameter99_name: Parameter name
        :param parameter99_value: Parameter value

        :returns: The created StreamInstance
        """

        data = values.of(
            {
                "Url": url,
                "Name": name,
                "Track": track,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "Parameter1.Name": parameter1_name,
                "Parameter1.Value": parameter1_value,
                "Parameter2.Name": parameter2_name,
                "Parameter2.Value": parameter2_value,
                "Parameter3.Name": parameter3_name,
                "Parameter3.Value": parameter3_value,
                "Parameter4.Name": parameter4_name,
                "Parameter4.Value": parameter4_value,
                "Parameter5.Name": parameter5_name,
                "Parameter5.Value": parameter5_value,
                "Parameter6.Name": parameter6_name,
                "Parameter6.Value": parameter6_value,
                "Parameter7.Name": parameter7_name,
                "Parameter7.Value": parameter7_value,
                "Parameter8.Name": parameter8_name,
                "Parameter8.Value": parameter8_value,
                "Parameter9.Name": parameter9_name,
                "Parameter9.Value": parameter9_value,
                "Parameter10.Name": parameter10_name,
                "Parameter10.Value": parameter10_value,
                "Parameter11.Name": parameter11_name,
                "Parameter11.Value": parameter11_value,
                "Parameter12.Name": parameter12_name,
                "Parameter12.Value": parameter12_value,
                "Parameter13.Name": parameter13_name,
                "Parameter13.Value": parameter13_value,
                "Parameter14.Name": parameter14_name,
                "Parameter14.Value": parameter14_value,
                "Parameter15.Name": parameter15_name,
                "Parameter15.Value": parameter15_value,
                "Parameter16.Name": parameter16_name,
                "Parameter16.Value": parameter16_value,
                "Parameter17.Name": parameter17_name,
                "Parameter17.Value": parameter17_value,
                "Parameter18.Name": parameter18_name,
                "Parameter18.Value": parameter18_value,
                "Parameter19.Name": parameter19_name,
                "Parameter19.Value": parameter19_value,
                "Parameter20.Name": parameter20_name,
                "Parameter20.Value": parameter20_value,
                "Parameter21.Name": parameter21_name,
                "Parameter21.Value": parameter21_value,
                "Parameter22.Name": parameter22_name,
                "Parameter22.Value": parameter22_value,
                "Parameter23.Name": parameter23_name,
                "Parameter23.Value": parameter23_value,
                "Parameter24.Name": parameter24_name,
                "Parameter24.Value": parameter24_value,
                "Parameter25.Name": parameter25_name,
                "Parameter25.Value": parameter25_value,
                "Parameter26.Name": parameter26_name,
                "Parameter26.Value": parameter26_value,
                "Parameter27.Name": parameter27_name,
                "Parameter27.Value": parameter27_value,
                "Parameter28.Name": parameter28_name,
                "Parameter28.Value": parameter28_value,
                "Parameter29.Name": parameter29_name,
                "Parameter29.Value": parameter29_value,
                "Parameter30.Name": parameter30_name,
                "Parameter30.Value": parameter30_value,
                "Parameter31.Name": parameter31_name,
                "Parameter31.Value": parameter31_value,
                "Parameter32.Name": parameter32_name,
                "Parameter32.Value": parameter32_value,
                "Parameter33.Name": parameter33_name,
                "Parameter33.Value": parameter33_value,
                "Parameter34.Name": parameter34_name,
                "Parameter34.Value": parameter34_value,
                "Parameter35.Name": parameter35_name,
                "Parameter35.Value": parameter35_value,
                "Parameter36.Name": parameter36_name,
                "Parameter36.Value": parameter36_value,
                "Parameter37.Name": parameter37_name,
                "Parameter37.Value": parameter37_value,
                "Parameter38.Name": parameter38_name,
                "Parameter38.Value": parameter38_value,
                "Parameter39.Name": parameter39_name,
                "Parameter39.Value": parameter39_value,
                "Parameter40.Name": parameter40_name,
                "Parameter40.Value": parameter40_value,
                "Parameter41.Name": parameter41_name,
                "Parameter41.Value": parameter41_value,
                "Parameter42.Name": parameter42_name,
                "Parameter42.Value": parameter42_value,
                "Parameter43.Name": parameter43_name,
                "Parameter43.Value": parameter43_value,
                "Parameter44.Name": parameter44_name,
                "Parameter44.Value": parameter44_value,
                "Parameter45.Name": parameter45_name,
                "Parameter45.Value": parameter45_value,
                "Parameter46.Name": parameter46_name,
                "Parameter46.Value": parameter46_value,
                "Parameter47.Name": parameter47_name,
                "Parameter47.Value": parameter47_value,
                "Parameter48.Name": parameter48_name,
                "Parameter48.Value": parameter48_value,
                "Parameter49.Name": parameter49_name,
                "Parameter49.Value": parameter49_value,
                "Parameter50.Name": parameter50_name,
                "Parameter50.Value": parameter50_value,
                "Parameter51.Name": parameter51_name,
                "Parameter51.Value": parameter51_value,
                "Parameter52.Name": parameter52_name,
                "Parameter52.Value": parameter52_value,
                "Parameter53.Name": parameter53_name,
                "Parameter53.Value": parameter53_value,
                "Parameter54.Name": parameter54_name,
                "Parameter54.Value": parameter54_value,
                "Parameter55.Name": parameter55_name,
                "Parameter55.Value": parameter55_value,
                "Parameter56.Name": parameter56_name,
                "Parameter56.Value": parameter56_value,
                "Parameter57.Name": parameter57_name,
                "Parameter57.Value": parameter57_value,
                "Parameter58.Name": parameter58_name,
                "Parameter58.Value": parameter58_value,
                "Parameter59.Name": parameter59_name,
                "Parameter59.Value": parameter59_value,
                "Parameter60.Name": parameter60_name,
                "Parameter60.Value": parameter60_value,
                "Parameter61.Name": parameter61_name,
                "Parameter61.Value": parameter61_value,
                "Parameter62.Name": parameter62_name,
                "Parameter62.Value": parameter62_value,
                "Parameter63.Name": parameter63_name,
                "Parameter63.Value": parameter63_value,
                "Parameter64.Name": parameter64_name,
                "Parameter64.Value": parameter64_value,
                "Parameter65.Name": parameter65_name,
                "Parameter65.Value": parameter65_value,
                "Parameter66.Name": parameter66_name,
                "Parameter66.Value": parameter66_value,
                "Parameter67.Name": parameter67_name,
                "Parameter67.Value": parameter67_value,
                "Parameter68.Name": parameter68_name,
                "Parameter68.Value": parameter68_value,
                "Parameter69.Name": parameter69_name,
                "Parameter69.Value": parameter69_value,
                "Parameter70.Name": parameter70_name,
                "Parameter70.Value": parameter70_value,
                "Parameter71.Name": parameter71_name,
                "Parameter71.Value": parameter71_value,
                "Parameter72.Name": parameter72_name,
                "Parameter72.Value": parameter72_value,
                "Parameter73.Name": parameter73_name,
                "Parameter73.Value": parameter73_value,
                "Parameter74.Name": parameter74_name,
                "Parameter74.Value": parameter74_value,
                "Parameter75.Name": parameter75_name,
                "Parameter75.Value": parameter75_value,
                "Parameter76.Name": parameter76_name,
                "Parameter76.Value": parameter76_value,
                "Parameter77.Name": parameter77_name,
                "Parameter77.Value": parameter77_value,
                "Parameter78.Name": parameter78_name,
                "Parameter78.Value": parameter78_value,
                "Parameter79.Name": parameter79_name,
                "Parameter79.Value": parameter79_value,
                "Parameter80.Name": parameter80_name,
                "Parameter80.Value": parameter80_value,
                "Parameter81.Name": parameter81_name,
                "Parameter81.Value": parameter81_value,
                "Parameter82.Name": parameter82_name,
                "Parameter82.Value": parameter82_value,
                "Parameter83.Name": parameter83_name,
                "Parameter83.Value": parameter83_value,
                "Parameter84.Name": parameter84_name,
                "Parameter84.Value": parameter84_value,
                "Parameter85.Name": parameter85_name,
                "Parameter85.Value": parameter85_value,
                "Parameter86.Name": parameter86_name,
                "Parameter86.Value": parameter86_value,
                "Parameter87.Name": parameter87_name,
                "Parameter87.Value": parameter87_value,
                "Parameter88.Name": parameter88_name,
                "Parameter88.Value": parameter88_value,
                "Parameter89.Name": parameter89_name,
                "Parameter89.Value": parameter89_value,
                "Parameter90.Name": parameter90_name,
                "Parameter90.Value": parameter90_value,
                "Parameter91.Name": parameter91_name,
                "Parameter91.Value": parameter91_value,
                "Parameter92.Name": parameter92_name,
                "Parameter92.Value": parameter92_value,
                "Parameter93.Name": parameter93_name,
                "Parameter93.Value": parameter93_value,
                "Parameter94.Name": parameter94_name,
                "Parameter94.Value": parameter94_value,
                "Parameter95.Name": parameter95_name,
                "Parameter95.Value": parameter95_value,
                "Parameter96.Name": parameter96_name,
                "Parameter96.Value": parameter96_value,
                "Parameter97.Name": parameter97_name,
                "Parameter97.Value": parameter97_value,
                "Parameter98.Name": parameter98_name,
                "Parameter98.Value": parameter98_value,
                "Parameter99.Name": parameter99_name,
                "Parameter99.Value": parameter99_value,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return StreamInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            call_sid=self._solution["call_sid"],
        )

    def get(self, sid: str) -> StreamContext:
        """
        Constructs a StreamContext

        :param sid: The SID or the `name` of the Stream resource to be stopped
        """
        return StreamContext(
            self._version,
            account_sid=self._solution["account_sid"],
            call_sid=self._solution["call_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> StreamContext:
        """
        Constructs a StreamContext

        :param sid: The SID or the `name` of the Stream resource to be stopped
        """
        return StreamContext(
            self._version,
            account_sid=self._solution["account_sid"],
            call_sid=self._solution["call_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.StreamList>"
