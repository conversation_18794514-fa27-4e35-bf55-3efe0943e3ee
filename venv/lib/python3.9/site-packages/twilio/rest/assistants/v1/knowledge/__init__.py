r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Assistants
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.assistants.v1.knowledge.chunk import ChunkList
from twilio.rest.assistants.v1.knowledge.knowledge_status import KnowledgeStatusList


class KnowledgeInstance(InstanceResource):

    class AssistantsV1ServiceCreateKnowledgeRequest(object):
        """
        :ivar assistant_id: The Assistant ID.
        :ivar description: The description of the knowledge source.
        :ivar knowledge_source_details: The details of the knowledge source based on the type.
        :ivar name: The name of the tool.
        :ivar policy:
        :ivar type: The type of the knowledge source.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.assistant_id: Optional[str] = payload.get("assistant_id")
            self.description: Optional[str] = payload.get("description")
            self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
                "knowledge_source_details"
            )
            self.name: Optional[str] = payload.get("name")
            self.policy: Optional[
                KnowledgeList.AssistantsV1ServiceCreatePolicyRequest
            ] = payload.get("policy")
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "assistant_id": self.assistant_id,
                "description": self.description,
                "knowledge_source_details": self.knowledge_source_details,
                "name": self.name,
                "policy": self.policy.to_dict() if self.policy is not None else None,
                "type": self.type,
            }

    class AssistantsV1ServiceCreatePolicyRequest(object):
        """
        :ivar description: The description of the policy.
        :ivar id: The Policy ID.
        :ivar name: The name of the policy.
        :ivar policy_details:
        :ivar type: The description of the policy.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.description: Optional[str] = payload.get("description")
            self.id: Optional[str] = payload.get("id")
            self.name: Optional[str] = payload.get("name")
            self.policy_details: Optional[Dict[str, object]] = payload.get(
                "policy_details"
            )
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "description": self.description,
                "id": self.id,
                "name": self.name,
                "policy_details": self.policy_details,
                "type": self.type,
            }

    class AssistantsV1ServiceUpdateKnowledgeRequest(object):
        """
        :ivar description: The description of the knowledge source.
        :ivar knowledge_source_details: The details of the knowledge source based on the type.
        :ivar name: The name of the knowledge source.
        :ivar policy:
        :ivar type: The description of the knowledge source.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.description: Optional[str] = payload.get("description")
            self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
                "knowledge_source_details"
            )
            self.name: Optional[str] = payload.get("name")
            self.policy: Optional[
                KnowledgeList.AssistantsV1ServiceCreatePolicyRequest
            ] = payload.get("policy")
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "description": self.description,
                "knowledge_source_details": self.knowledge_source_details,
                "name": self.name,
                "policy": self.policy.to_dict() if self.policy is not None else None,
                "type": self.type,
            }

    """
    :ivar description: The type of knowledge source.
    :ivar id: The description of knowledge.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Knowledge resource.
    :ivar knowledge_source_details: The details of the knowledge source based on the type.
    :ivar name: The name of the knowledge source.
    :ivar status: The status of processing the knowledge source ('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED')
    :ivar type: The type of knowledge source ('Web', 'Database', 'Text', 'File')
    :ivar url: The url of the knowledge resource.
    :ivar date_created: The date and time in GMT when the Knowledge was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the Knowledge was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], id: Optional[str] = None
    ):
        super().__init__(version)

        self.description: Optional[str] = payload.get("description")
        self.id: Optional[str] = payload.get("id")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
            "knowledge_source_details"
        )
        self.name: Optional[str] = payload.get("name")
        self.status: Optional[str] = payload.get("status")
        self.type: Optional[str] = payload.get("type")
        self.url: Optional[str] = payload.get("url")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

        self._solution = {
            "id": id or self.id,
        }
        self._context: Optional[KnowledgeContext] = None

    @property
    def _proxy(self) -> "KnowledgeContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: KnowledgeContext for this KnowledgeInstance
        """
        if self._context is None:
            self._context = KnowledgeContext(
                self._version,
                id=self._solution["id"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the KnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the KnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "KnowledgeInstance":
        """
        Fetch the KnowledgeInstance


        :returns: The fetched KnowledgeInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "KnowledgeInstance":
        """
        Asynchronous coroutine to fetch the KnowledgeInstance


        :returns: The fetched KnowledgeInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        assistants_v1_service_update_knowledge_request: Union[
            AssistantsV1ServiceUpdateKnowledgeRequest, object
        ] = values.unset,
    ) -> "KnowledgeInstance":
        """
        Update the KnowledgeInstance

        :param assistants_v1_service_update_knowledge_request:

        :returns: The updated KnowledgeInstance
        """
        return self._proxy.update(
            assistants_v1_service_update_knowledge_request=assistants_v1_service_update_knowledge_request,
        )

    async def update_async(
        self,
        assistants_v1_service_update_knowledge_request: Union[
            AssistantsV1ServiceUpdateKnowledgeRequest, object
        ] = values.unset,
    ) -> "KnowledgeInstance":
        """
        Asynchronous coroutine to update the KnowledgeInstance

        :param assistants_v1_service_update_knowledge_request:

        :returns: The updated KnowledgeInstance
        """
        return await self._proxy.update_async(
            assistants_v1_service_update_knowledge_request=assistants_v1_service_update_knowledge_request,
        )

    @property
    def chunks(self) -> ChunkList:
        """
        Access the chunks
        """
        return self._proxy.chunks

    @property
    def knowledge_status(self) -> KnowledgeStatusList:
        """
        Access the knowledge_status
        """
        return self._proxy.knowledge_status

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.KnowledgeInstance {}>".format(context)


class KnowledgeContext(InstanceContext):

    class AssistantsV1ServiceCreateKnowledgeRequest(object):
        """
        :ivar assistant_id: The Assistant ID.
        :ivar description: The description of the knowledge source.
        :ivar knowledge_source_details: The details of the knowledge source based on the type.
        :ivar name: The name of the tool.
        :ivar policy:
        :ivar type: The type of the knowledge source.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.assistant_id: Optional[str] = payload.get("assistant_id")
            self.description: Optional[str] = payload.get("description")
            self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
                "knowledge_source_details"
            )
            self.name: Optional[str] = payload.get("name")
            self.policy: Optional[
                KnowledgeList.AssistantsV1ServiceCreatePolicyRequest
            ] = payload.get("policy")
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "assistant_id": self.assistant_id,
                "description": self.description,
                "knowledge_source_details": self.knowledge_source_details,
                "name": self.name,
                "policy": self.policy.to_dict() if self.policy is not None else None,
                "type": self.type,
            }

    class AssistantsV1ServiceCreatePolicyRequest(object):
        """
        :ivar description: The description of the policy.
        :ivar id: The Policy ID.
        :ivar name: The name of the policy.
        :ivar policy_details:
        :ivar type: The description of the policy.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.description: Optional[str] = payload.get("description")
            self.id: Optional[str] = payload.get("id")
            self.name: Optional[str] = payload.get("name")
            self.policy_details: Optional[Dict[str, object]] = payload.get(
                "policy_details"
            )
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "description": self.description,
                "id": self.id,
                "name": self.name,
                "policy_details": self.policy_details,
                "type": self.type,
            }

    class AssistantsV1ServiceUpdateKnowledgeRequest(object):
        """
        :ivar description: The description of the knowledge source.
        :ivar knowledge_source_details: The details of the knowledge source based on the type.
        :ivar name: The name of the knowledge source.
        :ivar policy:
        :ivar type: The description of the knowledge source.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.description: Optional[str] = payload.get("description")
            self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
                "knowledge_source_details"
            )
            self.name: Optional[str] = payload.get("name")
            self.policy: Optional[
                KnowledgeList.AssistantsV1ServiceCreatePolicyRequest
            ] = payload.get("policy")
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "description": self.description,
                "knowledge_source_details": self.knowledge_source_details,
                "name": self.name,
                "policy": self.policy.to_dict() if self.policy is not None else None,
                "type": self.type,
            }

    def __init__(self, version: Version, id: str):
        """
        Initialize the KnowledgeContext

        :param version: Version that contains the resource
        :param id:
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "id": id,
        }
        self._uri = "/Knowledge/{id}".format(**self._solution)

        self._chunks: Optional[ChunkList] = None
        self._knowledge_status: Optional[KnowledgeStatusList] = None

    def delete(self) -> bool:
        """
        Deletes the KnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the KnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> KnowledgeInstance:
        """
        Fetch the KnowledgeInstance


        :returns: The fetched KnowledgeInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return KnowledgeInstance(
            self._version,
            payload,
            id=self._solution["id"],
        )

    async def fetch_async(self) -> KnowledgeInstance:
        """
        Asynchronous coroutine to fetch the KnowledgeInstance


        :returns: The fetched KnowledgeInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return KnowledgeInstance(
            self._version,
            payload,
            id=self._solution["id"],
        )

    def update(
        self,
        assistants_v1_service_update_knowledge_request: Union[
            AssistantsV1ServiceUpdateKnowledgeRequest, object
        ] = values.unset,
    ) -> KnowledgeInstance:
        """
        Update the KnowledgeInstance

        :param assistants_v1_service_update_knowledge_request:

        :returns: The updated KnowledgeInstance
        """
        data = assistants_v1_service_update_knowledge_request.to_dict()

        headers = values.of({})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="PUT", uri=self._uri, data=data, headers=headers
        )

        return KnowledgeInstance(self._version, payload, id=self._solution["id"])

    async def update_async(
        self,
        assistants_v1_service_update_knowledge_request: Union[
            AssistantsV1ServiceUpdateKnowledgeRequest, object
        ] = values.unset,
    ) -> KnowledgeInstance:
        """
        Asynchronous coroutine to update the KnowledgeInstance

        :param assistants_v1_service_update_knowledge_request:

        :returns: The updated KnowledgeInstance
        """
        data = assistants_v1_service_update_knowledge_request.to_dict()

        headers = values.of({})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="PUT", uri=self._uri, data=data, headers=headers
        )

        return KnowledgeInstance(self._version, payload, id=self._solution["id"])

    @property
    def chunks(self) -> ChunkList:
        """
        Access the chunks
        """
        if self._chunks is None:
            self._chunks = ChunkList(
                self._version,
                self._solution["id"],
            )
        return self._chunks

    @property
    def knowledge_status(self) -> KnowledgeStatusList:
        """
        Access the knowledge_status
        """
        if self._knowledge_status is None:
            self._knowledge_status = KnowledgeStatusList(
                self._version,
                self._solution["id"],
            )
        return self._knowledge_status

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.KnowledgeContext {}>".format(context)


class KnowledgePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> KnowledgeInstance:
        """
        Build an instance of KnowledgeInstance

        :param payload: Payload response from the API
        """
        return KnowledgeInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.KnowledgePage>"


class KnowledgeList(ListResource):

    class AssistantsV1ServiceCreateKnowledgeRequest(object):
        """
        :ivar assistant_id: The Assistant ID.
        :ivar description: The description of the knowledge source.
        :ivar knowledge_source_details: The details of the knowledge source based on the type.
        :ivar name: The name of the tool.
        :ivar policy:
        :ivar type: The type of the knowledge source.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.assistant_id: Optional[str] = payload.get("assistant_id")
            self.description: Optional[str] = payload.get("description")
            self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
                "knowledge_source_details"
            )
            self.name: Optional[str] = payload.get("name")
            self.policy: Optional[
                KnowledgeList.AssistantsV1ServiceCreatePolicyRequest
            ] = payload.get("policy")
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "assistant_id": self.assistant_id,
                "description": self.description,
                "knowledge_source_details": self.knowledge_source_details,
                "name": self.name,
                "policy": self.policy.to_dict() if self.policy is not None else None,
                "type": self.type,
            }

    class AssistantsV1ServiceCreatePolicyRequest(object):
        """
        :ivar description: The description of the policy.
        :ivar id: The Policy ID.
        :ivar name: The name of the policy.
        :ivar policy_details:
        :ivar type: The description of the policy.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.description: Optional[str] = payload.get("description")
            self.id: Optional[str] = payload.get("id")
            self.name: Optional[str] = payload.get("name")
            self.policy_details: Optional[Dict[str, object]] = payload.get(
                "policy_details"
            )
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "description": self.description,
                "id": self.id,
                "name": self.name,
                "policy_details": self.policy_details,
                "type": self.type,
            }

    class AssistantsV1ServiceUpdateKnowledgeRequest(object):
        """
        :ivar description: The description of the knowledge source.
        :ivar knowledge_source_details: The details of the knowledge source based on the type.
        :ivar name: The name of the knowledge source.
        :ivar policy:
        :ivar type: The description of the knowledge source.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.description: Optional[str] = payload.get("description")
            self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
                "knowledge_source_details"
            )
            self.name: Optional[str] = payload.get("name")
            self.policy: Optional[
                KnowledgeList.AssistantsV1ServiceCreatePolicyRequest
            ] = payload.get("policy")
            self.type: Optional[str] = payload.get("type")

        def to_dict(self):
            return {
                "description": self.description,
                "knowledge_source_details": self.knowledge_source_details,
                "name": self.name,
                "policy": self.policy.to_dict() if self.policy is not None else None,
                "type": self.type,
            }

    def __init__(self, version: Version):
        """
        Initialize the KnowledgeList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Knowledge"

    def create(
        self,
        assistants_v1_service_create_knowledge_request: AssistantsV1ServiceCreateKnowledgeRequest,
    ) -> KnowledgeInstance:
        """
        Create the KnowledgeInstance

        :param assistants_v1_service_create_knowledge_request:

        :returns: The created KnowledgeInstance
        """
        data = assistants_v1_service_create_knowledge_request.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return KnowledgeInstance(self._version, payload)

    async def create_async(
        self,
        assistants_v1_service_create_knowledge_request: AssistantsV1ServiceCreateKnowledgeRequest,
    ) -> KnowledgeInstance:
        """
        Asynchronously create the KnowledgeInstance

        :param assistants_v1_service_create_knowledge_request:

        :returns: The created KnowledgeInstance
        """
        data = assistants_v1_service_create_knowledge_request.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return KnowledgeInstance(self._version, payload)

    def stream(
        self,
        assistant_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[KnowledgeInstance]:
        """
        Streams KnowledgeInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str assistant_id:
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(assistant_id=assistant_id, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        assistant_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[KnowledgeInstance]:
        """
        Asynchronously streams KnowledgeInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str assistant_id:
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            assistant_id=assistant_id, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        assistant_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[KnowledgeInstance]:
        """
        Lists KnowledgeInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str assistant_id:
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                assistant_id=assistant_id,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        assistant_id: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[KnowledgeInstance]:
        """
        Asynchronously lists KnowledgeInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str assistant_id:
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                assistant_id=assistant_id,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        assistant_id: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> KnowledgePage:
        """
        Retrieve a single page of KnowledgeInstance records from the API.
        Request is executed immediately

        :param assistant_id:
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of KnowledgeInstance
        """
        data = values.of(
            {
                "AssistantId": assistant_id,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return KnowledgePage(self._version, response)

    async def page_async(
        self,
        assistant_id: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> KnowledgePage:
        """
        Asynchronously retrieve a single page of KnowledgeInstance records from the API.
        Request is executed immediately

        :param assistant_id:
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of KnowledgeInstance
        """
        data = values.of(
            {
                "AssistantId": assistant_id,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return KnowledgePage(self._version, response)

    def get_page(self, target_url: str) -> KnowledgePage:
        """
        Retrieve a specific page of KnowledgeInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of KnowledgeInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return KnowledgePage(self._version, response)

    async def get_page_async(self, target_url: str) -> KnowledgePage:
        """
        Asynchronously retrieve a specific page of KnowledgeInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of KnowledgeInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return KnowledgePage(self._version, response)

    def get(self, id: str) -> KnowledgeContext:
        """
        Constructs a KnowledgeContext

        :param id:
        """
        return KnowledgeContext(self._version, id=id)

    def __call__(self, id: str) -> KnowledgeContext:
        """
        Constructs a KnowledgeContext

        :param id:
        """
        return KnowledgeContext(self._version, id=id)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.KnowledgeList>"
