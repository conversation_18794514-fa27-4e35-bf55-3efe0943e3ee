r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Assistants
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class AssistantsKnowledgeInstance(InstanceResource):
    """
    :ivar description: The type of knowledge source.
    :ivar id: The description of knowledge.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Knowledge resource.
    :ivar knowledge_source_details: The details of the knowledge source based on the type.
    :ivar name: The name of the knowledge source.
    :ivar status: The status of processing the knowledge source ('QUEUED', 'PROCESSING', 'COMPLETED', 'FAILED')
    :ivar type: The type of knowledge source ('Web', 'Database', 'Text', 'File')
    :ivar url: The url of the knowledge resource.
    :ivar date_created: The date and time in GMT when the Knowledge was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the Knowledge was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        assistant_id: str,
        id: Optional[str] = None,
    ):
        super().__init__(version)

        self.description: Optional[str] = payload.get("description")
        self.id: Optional[str] = payload.get("id")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.knowledge_source_details: Optional[Dict[str, object]] = payload.get(
            "knowledge_source_details"
        )
        self.name: Optional[str] = payload.get("name")
        self.status: Optional[str] = payload.get("status")
        self.type: Optional[str] = payload.get("type")
        self.url: Optional[str] = payload.get("url")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

        self._solution = {
            "assistant_id": assistant_id,
            "id": id or self.id,
        }
        self._context: Optional[AssistantsKnowledgeContext] = None

    @property
    def _proxy(self) -> "AssistantsKnowledgeContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: AssistantsKnowledgeContext for this AssistantsKnowledgeInstance
        """
        if self._context is None:
            self._context = AssistantsKnowledgeContext(
                self._version,
                assistant_id=self._solution["assistant_id"],
                id=self._solution["id"],
            )
        return self._context

    def create(self) -> "AssistantsKnowledgeInstance":
        """
        Create the AssistantsKnowledgeInstance


        :returns: The created AssistantsKnowledgeInstance
        """
        return self._proxy.create()

    async def create_async(self) -> "AssistantsKnowledgeInstance":
        """
        Asynchronous coroutine to create the AssistantsKnowledgeInstance


        :returns: The created AssistantsKnowledgeInstance
        """
        return await self._proxy.create_async()

    def delete(self) -> bool:
        """
        Deletes the AssistantsKnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the AssistantsKnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.AssistantsKnowledgeInstance {}>".format(context)


class AssistantsKnowledgeContext(InstanceContext):

    def __init__(self, version: Version, assistant_id: str, id: str):
        """
        Initialize the AssistantsKnowledgeContext

        :param version: Version that contains the resource
        :param assistant_id: The assistant ID.
        :param id: The knowledge ID.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "assistant_id": assistant_id,
            "id": id,
        }
        self._uri = "/Assistants/{assistant_id}/Knowledge/{id}".format(**self._solution)

    def create(self) -> AssistantsKnowledgeInstance:
        """
        Create the AssistantsKnowledgeInstance


        :returns: The created AssistantsKnowledgeInstance
        """
        data = values.of({})

        payload = self._version.create(method="POST", uri=self._uri, data=data)

        return AssistantsKnowledgeInstance(
            self._version,
            payload,
            assistant_id=self._solution["assistant_id"],
            id=self._solution["id"],
        )

    async def create_async(self) -> AssistantsKnowledgeInstance:
        """
        Asynchronous coroutine to create the AssistantsKnowledgeInstance


        :returns: The created AssistantsKnowledgeInstance
        """
        data = values.of({})

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data
        )

        return AssistantsKnowledgeInstance(
            self._version,
            payload,
            assistant_id=self._solution["assistant_id"],
            id=self._solution["id"],
        )

    def delete(self) -> bool:
        """
        Deletes the AssistantsKnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the AssistantsKnowledgeInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.AssistantsKnowledgeContext {}>".format(context)


class AssistantsKnowledgePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> AssistantsKnowledgeInstance:
        """
        Build an instance of AssistantsKnowledgeInstance

        :param payload: Payload response from the API
        """
        return AssistantsKnowledgeInstance(
            self._version, payload, assistant_id=self._solution["assistant_id"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.AssistantsKnowledgePage>"


class AssistantsKnowledgeList(ListResource):

    def __init__(self, version: Version, assistant_id: str):
        """
        Initialize the AssistantsKnowledgeList

        :param version: Version that contains the resource
        :param assistant_id: The assistant ID.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "assistant_id": assistant_id,
        }
        self._uri = "/Assistants/{assistant_id}/Knowledge".format(**self._solution)

    def create(self) -> AssistantsKnowledgeInstance:
        """
        Create the AssistantsKnowledgeInstance


        :returns: The created AssistantsKnowledgeInstance
        """

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        payload = self._version.create(method="POST", uri=self._uri, headers=headers)

        return AssistantsKnowledgeInstance(
            self._version, payload, assistant_id=self._solution["assistant_id"]
        )

    async def create_async(self) -> AssistantsKnowledgeInstance:
        """
        Asynchronously create the AssistantsKnowledgeInstance


        :returns: The created AssistantsKnowledgeInstance
        """

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        payload = await self._version.create_async(
            method="POST", uri=self._uri, headers=headers
        )

        return AssistantsKnowledgeInstance(
            self._version, payload, assistant_id=self._solution["assistant_id"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[AssistantsKnowledgeInstance]:
        """
        Streams AssistantsKnowledgeInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[AssistantsKnowledgeInstance]:
        """
        Asynchronously streams AssistantsKnowledgeInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[AssistantsKnowledgeInstance]:
        """
        Lists AssistantsKnowledgeInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[AssistantsKnowledgeInstance]:
        """
        Asynchronously lists AssistantsKnowledgeInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> AssistantsKnowledgePage:
        """
        Retrieve a single page of AssistantsKnowledgeInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of AssistantsKnowledgeInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return AssistantsKnowledgePage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> AssistantsKnowledgePage:
        """
        Asynchronously retrieve a single page of AssistantsKnowledgeInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of AssistantsKnowledgeInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return AssistantsKnowledgePage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> AssistantsKnowledgePage:
        """
        Retrieve a specific page of AssistantsKnowledgeInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of AssistantsKnowledgeInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return AssistantsKnowledgePage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> AssistantsKnowledgePage:
        """
        Asynchronously retrieve a specific page of AssistantsKnowledgeInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of AssistantsKnowledgeInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return AssistantsKnowledgePage(self._version, response, self._solution)

    def get(self, id: str) -> AssistantsKnowledgeContext:
        """
        Constructs a AssistantsKnowledgeContext

        :param id: The knowledge ID.
        """
        return AssistantsKnowledgeContext(
            self._version, assistant_id=self._solution["assistant_id"], id=id
        )

    def __call__(self, id: str) -> AssistantsKnowledgeContext:
        """
        Constructs a AssistantsKnowledgeContext

        :param id: The knowledge ID.
        """
        return AssistantsKnowledgeContext(
            self._version, assistant_id=self._solution["assistant_id"], id=id
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.AssistantsKnowledgeList>"
