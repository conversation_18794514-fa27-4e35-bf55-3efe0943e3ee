r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Conversations
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class NotificationInstance(InstanceResource):
    """
    :ivar account_sid: The unique ID of the [Account](https://www.twilio.com/docs/iam/api/account) responsible for this configuration.
    :ivar chat_service_sid: The SID of the [Conversation Service](https://www.twilio.com/docs/conversations/api/service-resource) the Configuration applies to.
    :ivar new_message: The Push Notification configuration for New Messages.
    :ivar added_to_conversation: The Push Notification configuration for being added to a Conversation.
    :ivar removed_from_conversation: The Push Notification configuration for being removed from a Conversation.
    :ivar log_enabled: Weather the notification logging is enabled.
    :ivar url: An absolute API resource URL for this configuration.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], chat_service_sid: str
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.chat_service_sid: Optional[str] = payload.get("chat_service_sid")
        self.new_message: Optional[Dict[str, object]] = payload.get("new_message")
        self.added_to_conversation: Optional[Dict[str, object]] = payload.get(
            "added_to_conversation"
        )
        self.removed_from_conversation: Optional[Dict[str, object]] = payload.get(
            "removed_from_conversation"
        )
        self.log_enabled: Optional[bool] = payload.get("log_enabled")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "chat_service_sid": chat_service_sid,
        }
        self._context: Optional[NotificationContext] = None

    @property
    def _proxy(self) -> "NotificationContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: NotificationContext for this NotificationInstance
        """
        if self._context is None:
            self._context = NotificationContext(
                self._version,
                chat_service_sid=self._solution["chat_service_sid"],
            )
        return self._context

    def fetch(self) -> "NotificationInstance":
        """
        Fetch the NotificationInstance


        :returns: The fetched NotificationInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "NotificationInstance":
        """
        Asynchronous coroutine to fetch the NotificationInstance


        :returns: The fetched NotificationInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        log_enabled: Union[bool, object] = values.unset,
        new_message_enabled: Union[bool, object] = values.unset,
        new_message_template: Union[str, object] = values.unset,
        new_message_sound: Union[str, object] = values.unset,
        new_message_badge_count_enabled: Union[bool, object] = values.unset,
        added_to_conversation_enabled: Union[bool, object] = values.unset,
        added_to_conversation_template: Union[str, object] = values.unset,
        added_to_conversation_sound: Union[str, object] = values.unset,
        removed_from_conversation_enabled: Union[bool, object] = values.unset,
        removed_from_conversation_template: Union[str, object] = values.unset,
        removed_from_conversation_sound: Union[str, object] = values.unset,
        new_message_with_media_enabled: Union[bool, object] = values.unset,
        new_message_with_media_template: Union[str, object] = values.unset,
    ) -> "NotificationInstance":
        """
        Update the NotificationInstance

        :param log_enabled: Weather the notification logging is enabled.
        :param new_message_enabled: Whether to send a notification when a new message is added to a conversation. The default is `false`.
        :param new_message_template: The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_sound: The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param added_to_conversation_enabled: Whether to send a notification when a participant is added to a conversation. The default is `false`.
        :param added_to_conversation_template: The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param added_to_conversation_sound: The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param removed_from_conversation_enabled: Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
        :param removed_from_conversation_template: The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param removed_from_conversation_sound: The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param new_message_with_media_enabled: Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
        :param new_message_with_media_template: The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.

        :returns: The updated NotificationInstance
        """
        return self._proxy.update(
            log_enabled=log_enabled,
            new_message_enabled=new_message_enabled,
            new_message_template=new_message_template,
            new_message_sound=new_message_sound,
            new_message_badge_count_enabled=new_message_badge_count_enabled,
            added_to_conversation_enabled=added_to_conversation_enabled,
            added_to_conversation_template=added_to_conversation_template,
            added_to_conversation_sound=added_to_conversation_sound,
            removed_from_conversation_enabled=removed_from_conversation_enabled,
            removed_from_conversation_template=removed_from_conversation_template,
            removed_from_conversation_sound=removed_from_conversation_sound,
            new_message_with_media_enabled=new_message_with_media_enabled,
            new_message_with_media_template=new_message_with_media_template,
        )

    async def update_async(
        self,
        log_enabled: Union[bool, object] = values.unset,
        new_message_enabled: Union[bool, object] = values.unset,
        new_message_template: Union[str, object] = values.unset,
        new_message_sound: Union[str, object] = values.unset,
        new_message_badge_count_enabled: Union[bool, object] = values.unset,
        added_to_conversation_enabled: Union[bool, object] = values.unset,
        added_to_conversation_template: Union[str, object] = values.unset,
        added_to_conversation_sound: Union[str, object] = values.unset,
        removed_from_conversation_enabled: Union[bool, object] = values.unset,
        removed_from_conversation_template: Union[str, object] = values.unset,
        removed_from_conversation_sound: Union[str, object] = values.unset,
        new_message_with_media_enabled: Union[bool, object] = values.unset,
        new_message_with_media_template: Union[str, object] = values.unset,
    ) -> "NotificationInstance":
        """
        Asynchronous coroutine to update the NotificationInstance

        :param log_enabled: Weather the notification logging is enabled.
        :param new_message_enabled: Whether to send a notification when a new message is added to a conversation. The default is `false`.
        :param new_message_template: The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_sound: The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param added_to_conversation_enabled: Whether to send a notification when a participant is added to a conversation. The default is `false`.
        :param added_to_conversation_template: The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param added_to_conversation_sound: The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param removed_from_conversation_enabled: Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
        :param removed_from_conversation_template: The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param removed_from_conversation_sound: The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param new_message_with_media_enabled: Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
        :param new_message_with_media_template: The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.

        :returns: The updated NotificationInstance
        """
        return await self._proxy.update_async(
            log_enabled=log_enabled,
            new_message_enabled=new_message_enabled,
            new_message_template=new_message_template,
            new_message_sound=new_message_sound,
            new_message_badge_count_enabled=new_message_badge_count_enabled,
            added_to_conversation_enabled=added_to_conversation_enabled,
            added_to_conversation_template=added_to_conversation_template,
            added_to_conversation_sound=added_to_conversation_sound,
            removed_from_conversation_enabled=removed_from_conversation_enabled,
            removed_from_conversation_template=removed_from_conversation_template,
            removed_from_conversation_sound=removed_from_conversation_sound,
            new_message_with_media_enabled=new_message_with_media_enabled,
            new_message_with_media_template=new_message_with_media_template,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Conversations.V1.NotificationInstance {}>".format(context)


class NotificationContext(InstanceContext):

    def __init__(self, version: Version, chat_service_sid: str):
        """
        Initialize the NotificationContext

        :param version: Version that contains the resource
        :param chat_service_sid: The SID of the [Conversation Service](https://www.twilio.com/docs/conversations/api/service-resource) the Configuration applies to.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "chat_service_sid": chat_service_sid,
        }
        self._uri = "/Services/{chat_service_sid}/Configuration/Notifications".format(
            **self._solution
        )

    def fetch(self) -> NotificationInstance:
        """
        Fetch the NotificationInstance


        :returns: The fetched NotificationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return NotificationInstance(
            self._version,
            payload,
            chat_service_sid=self._solution["chat_service_sid"],
        )

    async def fetch_async(self) -> NotificationInstance:
        """
        Asynchronous coroutine to fetch the NotificationInstance


        :returns: The fetched NotificationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return NotificationInstance(
            self._version,
            payload,
            chat_service_sid=self._solution["chat_service_sid"],
        )

    def update(
        self,
        log_enabled: Union[bool, object] = values.unset,
        new_message_enabled: Union[bool, object] = values.unset,
        new_message_template: Union[str, object] = values.unset,
        new_message_sound: Union[str, object] = values.unset,
        new_message_badge_count_enabled: Union[bool, object] = values.unset,
        added_to_conversation_enabled: Union[bool, object] = values.unset,
        added_to_conversation_template: Union[str, object] = values.unset,
        added_to_conversation_sound: Union[str, object] = values.unset,
        removed_from_conversation_enabled: Union[bool, object] = values.unset,
        removed_from_conversation_template: Union[str, object] = values.unset,
        removed_from_conversation_sound: Union[str, object] = values.unset,
        new_message_with_media_enabled: Union[bool, object] = values.unset,
        new_message_with_media_template: Union[str, object] = values.unset,
    ) -> NotificationInstance:
        """
        Update the NotificationInstance

        :param log_enabled: Weather the notification logging is enabled.
        :param new_message_enabled: Whether to send a notification when a new message is added to a conversation. The default is `false`.
        :param new_message_template: The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_sound: The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param added_to_conversation_enabled: Whether to send a notification when a participant is added to a conversation. The default is `false`.
        :param added_to_conversation_template: The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param added_to_conversation_sound: The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param removed_from_conversation_enabled: Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
        :param removed_from_conversation_template: The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param removed_from_conversation_sound: The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param new_message_with_media_enabled: Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
        :param new_message_with_media_template: The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.

        :returns: The updated NotificationInstance
        """

        data = values.of(
            {
                "LogEnabled": serialize.boolean_to_string(log_enabled),
                "NewMessage.Enabled": serialize.boolean_to_string(new_message_enabled),
                "NewMessage.Template": new_message_template,
                "NewMessage.Sound": new_message_sound,
                "NewMessage.BadgeCountEnabled": serialize.boolean_to_string(
                    new_message_badge_count_enabled
                ),
                "AddedToConversation.Enabled": serialize.boolean_to_string(
                    added_to_conversation_enabled
                ),
                "AddedToConversation.Template": added_to_conversation_template,
                "AddedToConversation.Sound": added_to_conversation_sound,
                "RemovedFromConversation.Enabled": serialize.boolean_to_string(
                    removed_from_conversation_enabled
                ),
                "RemovedFromConversation.Template": removed_from_conversation_template,
                "RemovedFromConversation.Sound": removed_from_conversation_sound,
                "NewMessage.WithMedia.Enabled": serialize.boolean_to_string(
                    new_message_with_media_enabled
                ),
                "NewMessage.WithMedia.Template": new_message_with_media_template,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return NotificationInstance(
            self._version, payload, chat_service_sid=self._solution["chat_service_sid"]
        )

    async def update_async(
        self,
        log_enabled: Union[bool, object] = values.unset,
        new_message_enabled: Union[bool, object] = values.unset,
        new_message_template: Union[str, object] = values.unset,
        new_message_sound: Union[str, object] = values.unset,
        new_message_badge_count_enabled: Union[bool, object] = values.unset,
        added_to_conversation_enabled: Union[bool, object] = values.unset,
        added_to_conversation_template: Union[str, object] = values.unset,
        added_to_conversation_sound: Union[str, object] = values.unset,
        removed_from_conversation_enabled: Union[bool, object] = values.unset,
        removed_from_conversation_template: Union[str, object] = values.unset,
        removed_from_conversation_sound: Union[str, object] = values.unset,
        new_message_with_media_enabled: Union[bool, object] = values.unset,
        new_message_with_media_template: Union[str, object] = values.unset,
    ) -> NotificationInstance:
        """
        Asynchronous coroutine to update the NotificationInstance

        :param log_enabled: Weather the notification logging is enabled.
        :param new_message_enabled: Whether to send a notification when a new message is added to a conversation. The default is `false`.
        :param new_message_template: The template to use to create the notification text displayed when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_sound: The name of the sound to play when a new message is added to a conversation and `new_message.enabled` is `true`.
        :param new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param added_to_conversation_enabled: Whether to send a notification when a participant is added to a conversation. The default is `false`.
        :param added_to_conversation_template: The template to use to create the notification text displayed when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param added_to_conversation_sound: The name of the sound to play when a participant is added to a conversation and `added_to_conversation.enabled` is `true`.
        :param removed_from_conversation_enabled: Whether to send a notification to a user when they are removed from a conversation. The default is `false`.
        :param removed_from_conversation_template: The template to use to create the notification text displayed to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param removed_from_conversation_sound: The name of the sound to play to a user when they are removed from a conversation and `removed_from_conversation.enabled` is `true`.
        :param new_message_with_media_enabled: Whether to send a notification when a new message with media/file attachments is added to a conversation. The default is `false`.
        :param new_message_with_media_template: The template to use to create the notification text displayed when a new message with media/file attachments is added to a conversation and `new_message.attachments.enabled` is `true`.

        :returns: The updated NotificationInstance
        """

        data = values.of(
            {
                "LogEnabled": serialize.boolean_to_string(log_enabled),
                "NewMessage.Enabled": serialize.boolean_to_string(new_message_enabled),
                "NewMessage.Template": new_message_template,
                "NewMessage.Sound": new_message_sound,
                "NewMessage.BadgeCountEnabled": serialize.boolean_to_string(
                    new_message_badge_count_enabled
                ),
                "AddedToConversation.Enabled": serialize.boolean_to_string(
                    added_to_conversation_enabled
                ),
                "AddedToConversation.Template": added_to_conversation_template,
                "AddedToConversation.Sound": added_to_conversation_sound,
                "RemovedFromConversation.Enabled": serialize.boolean_to_string(
                    removed_from_conversation_enabled
                ),
                "RemovedFromConversation.Template": removed_from_conversation_template,
                "RemovedFromConversation.Sound": removed_from_conversation_sound,
                "NewMessage.WithMedia.Enabled": serialize.boolean_to_string(
                    new_message_with_media_enabled
                ),
                "NewMessage.WithMedia.Template": new_message_with_media_template,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return NotificationInstance(
            self._version, payload, chat_service_sid=self._solution["chat_service_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Conversations.V1.NotificationContext {}>".format(context)


class NotificationList(ListResource):

    def __init__(self, version: Version, chat_service_sid: str):
        """
        Initialize the NotificationList

        :param version: Version that contains the resource
        :param chat_service_sid: The SID of the [Conversation Service](https://www.twilio.com/docs/conversations/api/service-resource) the Configuration applies to.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "chat_service_sid": chat_service_sid,
        }

    def get(self) -> NotificationContext:
        """
        Constructs a NotificationContext

        """
        return NotificationContext(
            self._version, chat_service_sid=self._solution["chat_service_sid"]
        )

    def __call__(self) -> NotificationContext:
        """
        Constructs a NotificationContext

        """
        return NotificationContext(
            self._version, chat_service_sid=self._solution["chat_service_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Conversations.V1.NotificationList>"
