r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Chat
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class UserBindingInstance(InstanceResource):

    class BindingType(object):
        GCM = "gcm"
        APN = "apn"
        FCM = "fcm"

    """
    :ivar sid: The unique string that we created to identify the User Binding resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the User Binding resource.
    :ivar service_sid: The SID of the [Service](https://www.twilio.com/docs/chat/rest/service-resource) the User Binding resource is associated with.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar endpoint: The unique endpoint identifier for the User Binding. The format of the value depends on the `binding_type`.
    :ivar identity: The application-defined string that uniquely identifies the resource's [User](https://www.twilio.com/docs/chat/rest/user-resource) within the [Service](https://www.twilio.com/docs/chat/rest/service-resource). See [access tokens](https://www.twilio.com/docs/chat/create-tokens) for more info.
    :ivar user_sid: The SID of the [User](https://www.twilio.com/docs/chat/rest/user-resource) with the User Binding resource.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
    :ivar credential_sid: The SID of the [Credential](https://www.twilio.com/docs/chat/rest/credential-resource) for the binding. See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
    :ivar binding_type: 
    :ivar message_types: The [Programmable Chat message types](https://www.twilio.com/docs/chat/push-notification-configuration#push-types) the binding is subscribed to.
    :ivar url: The absolute URL of the User Binding resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        service_sid: str,
        user_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.service_sid: Optional[str] = payload.get("service_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.endpoint: Optional[str] = payload.get("endpoint")
        self.identity: Optional[str] = payload.get("identity")
        self.user_sid: Optional[str] = payload.get("user_sid")
        self.credential_sid: Optional[str] = payload.get("credential_sid")
        self.binding_type: Optional["UserBindingInstance.BindingType"] = payload.get(
            "binding_type"
        )
        self.message_types: Optional[List[str]] = payload.get("message_types")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "service_sid": service_sid,
            "user_sid": user_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[UserBindingContext] = None

    @property
    def _proxy(self) -> "UserBindingContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: UserBindingContext for this UserBindingInstance
        """
        if self._context is None:
            self._context = UserBindingContext(
                self._version,
                service_sid=self._solution["service_sid"],
                user_sid=self._solution["user_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the UserBindingInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the UserBindingInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "UserBindingInstance":
        """
        Fetch the UserBindingInstance


        :returns: The fetched UserBindingInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "UserBindingInstance":
        """
        Asynchronous coroutine to fetch the UserBindingInstance


        :returns: The fetched UserBindingInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Chat.V2.UserBindingInstance {}>".format(context)


class UserBindingContext(InstanceContext):

    def __init__(self, version: Version, service_sid: str, user_sid: str, sid: str):
        """
        Initialize the UserBindingContext

        :param version: Version that contains the resource
        :param service_sid: The SID of the [Service](https://www.twilio.com/docs/chat/rest/service-resource) to fetch the User Binding resource from.
        :param user_sid: The SID of the [User](https://www.twilio.com/docs/chat/rest/user-resource) with the User Binding resource to fetch.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param sid: The SID of the User Binding resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "user_sid": user_sid,
            "sid": sid,
        }
        self._uri = "/Services/{service_sid}/Users/<USER>/Bindings/{sid}".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the UserBindingInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the UserBindingInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> UserBindingInstance:
        """
        Fetch the UserBindingInstance


        :returns: The fetched UserBindingInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return UserBindingInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            user_sid=self._solution["user_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> UserBindingInstance:
        """
        Asynchronous coroutine to fetch the UserBindingInstance


        :returns: The fetched UserBindingInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return UserBindingInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            user_sid=self._solution["user_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Chat.V2.UserBindingContext {}>".format(context)


class UserBindingPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> UserBindingInstance:
        """
        Build an instance of UserBindingInstance

        :param payload: Payload response from the API
        """
        return UserBindingInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            user_sid=self._solution["user_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Chat.V2.UserBindingPage>"


class UserBindingList(ListResource):

    def __init__(self, version: Version, service_sid: str, user_sid: str):
        """
        Initialize the UserBindingList

        :param version: Version that contains the resource
        :param service_sid: The SID of the [Service](https://www.twilio.com/docs/chat/rest/service-resource) to read the User Binding resources from.
        :param user_sid: The SID of the [User](https://www.twilio.com/docs/chat/rest/user-resource) with the User Binding resources to read.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "user_sid": user_sid,
        }
        self._uri = "/Services/{service_sid}/Users/<USER>/Bindings".format(
            **self._solution
        )

    def stream(
        self,
        binding_type: Union[
            List["UserBindingInstance.BindingType"], object
        ] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[UserBindingInstance]:
        """
        Streams UserBindingInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param List[&quot;UserBindingInstance.BindingType&quot;] binding_type: The push technology used by the User Binding resources to read. Can be: `apn`, `gcm`, or `fcm`.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(binding_type=binding_type, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        binding_type: Union[
            List["UserBindingInstance.BindingType"], object
        ] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[UserBindingInstance]:
        """
        Asynchronously streams UserBindingInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param List[&quot;UserBindingInstance.BindingType&quot;] binding_type: The push technology used by the User Binding resources to read. Can be: `apn`, `gcm`, or `fcm`.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            binding_type=binding_type, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        binding_type: Union[
            List["UserBindingInstance.BindingType"], object
        ] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[UserBindingInstance]:
        """
        Lists UserBindingInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param List[&quot;UserBindingInstance.BindingType&quot;] binding_type: The push technology used by the User Binding resources to read. Can be: `apn`, `gcm`, or `fcm`.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                binding_type=binding_type,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        binding_type: Union[
            List["UserBindingInstance.BindingType"], object
        ] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[UserBindingInstance]:
        """
        Asynchronously lists UserBindingInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param List[&quot;UserBindingInstance.BindingType&quot;] binding_type: The push technology used by the User Binding resources to read. Can be: `apn`, `gcm`, or `fcm`.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                binding_type=binding_type,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        binding_type: Union[
            List["UserBindingInstance.BindingType"], object
        ] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> UserBindingPage:
        """
        Retrieve a single page of UserBindingInstance records from the API.
        Request is executed immediately

        :param binding_type: The push technology used by the User Binding resources to read. Can be: `apn`, `gcm`, or `fcm`.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of UserBindingInstance
        """
        data = values.of(
            {
                "BindingType": serialize.map(binding_type, lambda e: e),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return UserBindingPage(self._version, response, self._solution)

    async def page_async(
        self,
        binding_type: Union[
            List["UserBindingInstance.BindingType"], object
        ] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> UserBindingPage:
        """
        Asynchronously retrieve a single page of UserBindingInstance records from the API.
        Request is executed immediately

        :param binding_type: The push technology used by the User Binding resources to read. Can be: `apn`, `gcm`, or `fcm`.  See [push notification configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of UserBindingInstance
        """
        data = values.of(
            {
                "BindingType": serialize.map(binding_type, lambda e: e),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return UserBindingPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> UserBindingPage:
        """
        Retrieve a specific page of UserBindingInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of UserBindingInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return UserBindingPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> UserBindingPage:
        """
        Asynchronously retrieve a specific page of UserBindingInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of UserBindingInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return UserBindingPage(self._version, response, self._solution)

    def get(self, sid: str) -> UserBindingContext:
        """
        Constructs a UserBindingContext

        :param sid: The SID of the User Binding resource to fetch.
        """
        return UserBindingContext(
            self._version,
            service_sid=self._solution["service_sid"],
            user_sid=self._solution["user_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> UserBindingContext:
        """
        Constructs a UserBindingContext

        :param sid: The SID of the User Binding resource to fetch.
        """
        return UserBindingContext(
            self._version,
            service_sid=self._solution["service_sid"],
            user_sid=self._solution["user_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Chat.V2.UserBindingList>"
