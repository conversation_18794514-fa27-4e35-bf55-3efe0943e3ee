version: '3.8'

services:
  webrtc-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    image: webrtc-frontend:latest
    container_name: webrtc-frontend
    ports:
      - "8080:80"
    restart: unless-stopped
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.webrtc-frontend.rule=Host(`your-domain.com`)"
      - "traefik.http.routers.webrtc-frontend.entrypoints=websecure"
      - "traefik.http.routers.webrtc-frontend.tls.certresolver=myresolver"
