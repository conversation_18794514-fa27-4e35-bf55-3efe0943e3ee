# Use the official Nginx image as base
FROM nginx:alpine

# Set the working directory
WORKDIR /usr/share/nginx/html

# Remove default nginx static assets
RUN rm -rf ./*

# Copy static files to nginx html directory
COPY index.html .
COPY script.js .
COPY styles.css .
COPY config.js .
# Note: test.html and TESTING_GUIDE.md are excluded via .dockerignore

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
